-- CreateEnum
CREATE TYPE "BindingType" AS ENUM ('NOTION', 'FEISHU');

-- CreateTable
CREATE TABLE "User" (
    "id" VARCHAR(255) NOT NULL,
    "name" VARCHAR(255),
    "state" VARCHAR(50),
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bindings" (
    "id" VARCHAR(36) NOT NULL,
    "user_id" VARCHAR(255) NOT NULL,
    "type" "BindingType" NOT NULL,
    "access_token" TEXT NOT NULL,
    "target_id" VARCHAR(255) NOT NULL,
    "target_name" VARCHAR(255),
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "bindings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Subscription" (
    "id" VARCHAR(36) NOT NULL,
    "user_id" VARCHAR(255) NOT NULL,
    "plan" VARCHAR(50) NOT NULL,
    "limit" INTEGER NOT NULL DEFAULT 1000,
    "start_date" TIMESTAMP(6) NOT NULL,
    "end_date" TIMESTAMP(6) NOT NULL,
    "status" VARCHAR(50) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Subscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UsageStat" (
    "id" VARCHAR(36) NOT NULL,
    "user_id" VARCHAR(255) NOT NULL,
    "year_month" INTEGER NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 0,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "UsageStat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WecomSyncCursor" (
    "id" VARCHAR(255) NOT NULL,
    "cursor" VARCHAR(255) NOT NULL,
    "updated_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "WecomSyncCursor_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "bindings" ADD CONSTRAINT "bindings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UsageStat" ADD CONSTRAINT "UsageStat_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
