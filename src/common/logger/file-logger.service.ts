import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class FileLoggerService extends Logger {
  private logPath: string;
  private maxFileSize: number = 20 * 1024 * 1024; // 20MB
  private maxFiles: number = 30; // 保留30个文件

  constructor(private configService: ConfigService) {
    super('FileLogger');
    this.logPath = this.configService.get('LOG_FILE_PATH', './logs');
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logPath)) {
      fs.mkdirSync(this.logPath, { recursive: true });
    }
  }

  private getLogFileName(level: string): string {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logPath, `${level}-${date}.log`);
  }

  private writeToFile(level: string, message: string, context?: string, trace?: string): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      context: context || 'Application',
      message,
      ...(trace && { trace }),
    };

    const logLine = JSON.stringify(logEntry) + '\n';
    const fileName = this.getLogFileName(level);

    try {
      // 检查文件大小，如果超过限制则轮转
      if (fs.existsSync(fileName)) {
        const stats = fs.statSync(fileName);
        if (stats.size > this.maxFileSize) {
          this.rotateLogFile(fileName);
        }
      }

      fs.appendFileSync(fileName, logLine);
    } catch (error) {
      // 如果写入文件失败，至少输出到控制台
      super.error(`Failed to write to log file: ${error.message}`);
    }
  }

  private rotateLogFile(fileName: string): void {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const rotatedName = fileName.replace('.log', `-${timestamp}.log`);
    
    try {
      fs.renameSync(fileName, rotatedName);
      this.cleanupOldFiles();
    } catch (error) {
      super.error(`Failed to rotate log file: ${error.message}`);
    }
  }

  private cleanupOldFiles(): void {
    try {
      const files = fs.readdirSync(this.logPath)
        .filter(file => file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logPath, file),
          mtime: fs.statSync(path.join(this.logPath, file)).mtime,
        }))
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

      // 删除超过最大文件数的旧文件
      if (files.length > this.maxFiles) {
        const filesToDelete = files.slice(this.maxFiles);
        filesToDelete.forEach(file => {
          try {
            fs.unlinkSync(file.path);
          } catch (error) {
            super.error(`Failed to delete old log file ${file.name}: ${error.message}`);
          }
        });
      }
    } catch (error) {
      super.error(`Failed to cleanup old log files: ${error.message}`);
    }
  }

  // 重写父类方法，添加文件写入功能
  log(message: any, context?: string): void {
    super.log(message, context);
    this.writeToFile('info', message, context);
  }

  error(message: any, trace?: string, context?: string): void {
    super.error(message, trace, context);
    this.writeToFile('error', message, context, trace);
  }

  warn(message: any, context?: string): void {
    super.warn(message, context);
    this.writeToFile('warn', message, context);
  }

  debug(message: any, context?: string): void {
    super.debug(message, context);
    this.writeToFile('debug', message, context);
  }

  verbose(message: any, context?: string): void {
    super.verbose(message, context);
    this.writeToFile('verbose', message, context);
  }

  // 业务关键日志方法
  logBusiness(message: string, data?: any, context?: string): void {
    const logMessage = data ? `${message} ${JSON.stringify(data)}` : message;
    this.log(logMessage, context);
    
    // 额外写入业务日志文件
    const timestamp = new Date().toISOString();
    const businessLogEntry = {
      timestamp,
      level: 'business',
      context: context || 'Business',
      message,
      data,
    };

    const businessLogLine = JSON.stringify(businessLogEntry) + '\n';
    const businessFileName = this.getLogFileName('business');

    try {
      fs.appendFileSync(businessFileName, businessLogLine);
    } catch (error) {
      super.error(`Failed to write business log: ${error.message}`);
    }
  }

  // 安全相关日志
  logSecurity(message: string, data?: any, context?: string): void {
    const logMessage = data ? `${message} ${JSON.stringify(data)}` : message;
    this.warn(logMessage, context);
    
    // 额外写入安全日志文件
    const timestamp = new Date().toISOString();
    const securityLogEntry = {
      timestamp,
      level: 'security',
      context: context || 'Security',
      message,
      data,
    };

    const securityLogLine = JSON.stringify(securityLogEntry) + '\n';
    const securityFileName = this.getLogFileName('security');

    try {
      fs.appendFileSync(securityFileName, securityLogLine);
    } catch (error) {
      super.error(`Failed to write security log: ${error.message}`);
    }
  }
}
