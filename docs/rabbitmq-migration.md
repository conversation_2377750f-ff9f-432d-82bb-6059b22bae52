# 🐰 消息中间件迁移：Redis → RabbitMQ

## 📋 迁移概述

本文档描述了FlashBookkeeping项目从Redis Bull队列迁移到RabbitMQ消息队列的完整过程。

## 🔄 迁移原因

1. **更好的消息持久化**: RabbitMQ提供更可靠的消息持久化机制
2. **更强的消息路由**: 支持复杂的消息路由和交换机模式
3. **更好的集群支持**: 原生支持高可用集群部署
4. **标准AMQP协议**: 更好的跨语言和跨平台兼容性
5. **更丰富的管理功能**: 内置Web管理界面和监控工具

## 🏗️ 架构变更

### 原架构 (Redis Bull)
```
WecomService → Bull Queue → Redis → Bull Processor
```

### 新架构 (RabbitMQ)
```
WecomService → RabbitMQ Client → RabbitMQ Server → Message Pattern Handler
```

## 📦 依赖变更

### 移除的依赖
```bash
pnpm remove @nestjs/bull bull ioredis
```

### 新增的依赖
```bash
pnpm add @nestjs/microservices amqplib amqp-connection-manager
pnpm add -D @types/amqplib
```

## 🔧 配置变更

### 环境变量更新
```env
# 旧配置 (Redis)
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""
REDIS_DB=0

# 新配置 (RabbitMQ)
RABBITMQ_URL="amqp://localhost:5672"
RABBITMQ_USER="guest"
RABBITMQ_PASSWORD="guest"
RABBITMQ_VHOST="/"
```

### 队列配置
```typescript
// 旧配置 (Bull)
BullModule.forRootAsync({
  redis: { host: 'localhost', port: 6379 },
  defaultJobOptions: { attempts: 3 }
})

// 新配置 (RabbitMQ)
ClientsModule.registerAsync([{
  transport: Transport.RMQ,
  options: {
    urls: ['amqp://localhost:5672'],
    queue: 'main_queue',
    queueOptions: { durable: true }
  }
}])
```

## 🔄 代码变更

### 1. QueueService 重构

#### 旧实现 (Bull)
```typescript
@Injectable()
export class QueueService {
  constructor(
    @InjectQueue('message-processing') private messageQueue: Queue,
  ) {}

  async addJob(data: any) {
    await this.messageQueue.add('process', data, { priority: 10 });
  }
}
```

#### 新实现 (RabbitMQ)
```typescript
@Injectable()
export class QueueService {
  constructor(
    @Inject('RABBITMQ_SERVICE') private readonly rabbitClient: ClientProxy,
  ) {}

  async addJob(data: any) {
    await firstValueFrom(
      this.rabbitClient.emit('process-message', { data, priority: 10 })
    );
  }
}
```

### 2. 消息处理器重构

#### 旧实现 (Bull Processor)
```typescript
@Processor('message-processing')
export class MessageProcessor {
  @Process('process-message')
  async handleMessage(job: Job<MessageJob>) {
    const data = job.data;
    // 处理逻辑
  }
}
```

#### 新实现 (RabbitMQ Handler)
```typescript
@Controller()
export class MessageProcessor {
  @MessagePattern('process-message')
  async handleMessage(@Payload() payload: { data: MessageJob }) {
    const data = payload.data;
    // 处理逻辑
  }
}
```

## 🚀 部署要求

### RabbitMQ 安装

#### Docker 部署
```bash
docker run -d --name rabbitmq \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=password \
  rabbitmq:3-management
```

#### 本地安装 (macOS)
```bash
brew install rabbitmq
brew services start rabbitmq
```

#### 本地安装 (Ubuntu)
```bash
sudo apt-get install rabbitmq-server
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server
```

### 管理界面
- URL: http://localhost:15672
- 默认用户名: guest
- 默认密码: guest

## 📊 功能对比

| 功能 | Redis Bull | RabbitMQ | 说明 |
|------|------------|----------|------|
| 消息持久化 | ✅ | ✅ | RabbitMQ更可靠 |
| 任务重试 | ✅ | ✅ | 都支持 |
| 优先级队列 | ✅ | ✅ | 都支持 |
| 延迟任务 | ✅ | ✅ | RabbitMQ需要插件 |
| 集群支持 | ⚠️ | ✅ | RabbitMQ原生支持 |
| 管理界面 | ⚠️ | ✅ | RabbitMQ内置 |
| 消息路由 | ❌ | ✅ | RabbitMQ独有 |
| 跨语言支持 | ❌ | ✅ | AMQP标准 |

## 🔍 监控和调试

### 队列状态监控
```typescript
// 新增统计方法
async getQueueStats() {
  return {
    messageProcessing: this.queueStats.messageProcessing,
    aiAnalysis: this.queueStats.aiAnalysis,
    dataSync: this.queueStats.dataSync,
    rabbitMqConnected: true,
  };
}
```

### RabbitMQ 管理命令
```bash
# 查看队列状态
rabbitmqctl list_queues

# 查看连接
rabbitmqctl list_connections

# 查看交换机
rabbitmqctl list_exchanges
```

## 🎯 迁移优势

1. **更好的可靠性**: 消息不会因为Redis重启而丢失
2. **更强的扩展性**: 支持多种消息模式和路由策略
3. **更好的监控**: 内置管理界面和丰富的监控指标
4. **标准化**: 基于AMQP标准，便于与其他系统集成
5. **高可用**: 原生支持集群和镜像队列

## 🚨 注意事项

1. **延迟任务**: RabbitMQ的延迟任务需要安装rabbitmq-delayed-message-exchange插件
2. **内存使用**: RabbitMQ可能比Redis使用更多内存
3. **学习成本**: AMQP概念比Redis队列复杂
4. **配置复杂度**: RabbitMQ的配置选项更多更复杂

## 🔧 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查RabbitMQ状态
   sudo systemctl status rabbitmq-server
   
   # 检查端口
   netstat -tlnp | grep 5672
   ```

2. **权限问题**
   ```bash
   # 创建用户
   rabbitmqctl add_user myuser mypassword
   rabbitmqctl set_user_tags myuser administrator
   rabbitmqctl set_permissions -p / myuser ".*" ".*" ".*"
   ```

3. **队列堆积**
   ```bash
   # 清空队列
   rabbitmqctl purge_queue main_queue
   ```

## 📈 性能优化

1. **连接池**: 使用连接池减少连接开销
2. **批量处理**: 批量发送和接收消息
3. **预取设置**: 合理设置prefetch count
4. **持久化策略**: 根据需求选择消息持久化级别

迁移完成后，系统将具备更强的消息处理能力和更好的可扩展性！🎉
