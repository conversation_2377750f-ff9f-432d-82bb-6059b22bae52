import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UsageService } from './usage.service';

@ApiTags('用量管理')
@Controller('usage')
export class UsageController {
  constructor(private readonly usageService: UsageService) {}

  @ApiOperation({
    summary: '获取当前使用统计',
    description: '获取用户当月的使用统计信息',
  })
  @Get(':userId/current')
  async getCurrentUsage(@Param('userId') userId: string) {
    return await this.usageService.getCurrentUsage(userId);
  }

  @ApiOperation({
    summary: '检查是否超出限额',
    description: '检查用户是否已超出使用限额',
  })
  @Get(':userId/check-limit')
  async checkLimit(@Param('userId') userId: string) {
    const isOver = await this.usageService.isOverLimit(userId);
    return { isOverLimit: isOver };
  }

  @ApiOperation({
    summary: '获取使用历史',
    description: '获取用户的历史使用统计',
  })
  @Get(':userId/history')
  async getUsageHistory(
    @Param('userId') userId: string,
    @Query('months') months?: string,
  ) {
    const monthsNum = months ? parseInt(months, 10) : 12;
    return await this.usageService.getUsageHistory(userId, monthsNum);
  }

  @ApiOperation({
    summary: '获取系统统计',
    description: '获取系统整体的使用统计信息',
  })
  @Get('system/stats')
  async getSystemStats() {
    return await this.usageService.getSystemStats();
  }
}
