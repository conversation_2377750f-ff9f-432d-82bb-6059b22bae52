import { WinstonModule, WinstonModuleOptions } from 'nest-winston';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import { ConfigService } from '@nestjs/config';

export const createWinstonLogger = (configService: ConfigService): WinstonModuleOptions => {
  const logLevel = configService.get('LOG_LEVEL', 'info');
  const logPath = configService.get('LOG_FILE_PATH', './logs');
  const nodeEnv = configService.get('NODE_ENV', 'development');

  // 自定义日志格式
  const logFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
      const logEntry = {
        timestamp,
        level,
        context: context || 'Application',
        message,
        ...(trace && { trace }),
        ...(Object.keys(meta).length > 0 && { meta }),
      };
      return JSON.stringify(logEntry);
    }),
  );

  // 控制台格式（开发环境更友好）
  const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'HH:mm:ss' }),
    winston.format.colorize({ all: true }),
    winston.format.printf(({ timestamp, level, message, context }) => {
      return `${timestamp} [${context || 'App'}] ${level}: ${message}`;
    }),
  );

  const transports: winston.transport[] = [];

  // 控制台输出
  if (nodeEnv === 'development') {
    transports.push(
      new winston.transports.Console({
        format: consoleFormat,
        level: 'debug',
      }),
    );
  } else {
    transports.push(
      new winston.transports.Console({
        format: logFormat,
        level: logLevel,
      }),
    );
  }

  // 文件输出 - 所有日志
  transports.push(
    new DailyRotateFile({
      filename: `${logPath}/application-%DATE%.log`,
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: logFormat,
      level: logLevel,
    }),
  );

  // 文件输出 - 错误日志
  transports.push(
    new DailyRotateFile({
      filename: `${logPath}/error-%DATE%.log`,
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '90d',
      format: logFormat,
      level: 'error',
    }),
  );

  // 文件输出 - 业务关键日志
  transports.push(
    new DailyRotateFile({
      filename: `${logPath}/business-%DATE%.log`,
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '90d',
      format: logFormat,
      level: 'info',
      // 只记录特定上下文的日志
      filter: (info) => {
        const businessContexts = [
          'WecomService',
          'AiService',
          'NotionService',
          'FeishuService',
          'PaymentService',
          'QueueService',
          'MessageProcessor',
          'AiAnalysisProcessor',
          'DataSyncProcessor',
        ];
        return businessContexts.includes(info.context);
      },
    }),
  );

  return {
    level: logLevel,
    transports,
    // 处理未捕获的异常
    exceptionHandlers: [
      new DailyRotateFile({
        filename: `${logPath}/exceptions-%DATE%.log`,
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '90d',
        format: logFormat,
      }),
    ],
    // 处理未处理的 Promise 拒绝
    rejectionHandlers: [
      new DailyRotateFile({
        filename: `${logPath}/rejections-%DATE%.log`,
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '90d',
        format: logFormat,
      }),
    ],
  };
};
