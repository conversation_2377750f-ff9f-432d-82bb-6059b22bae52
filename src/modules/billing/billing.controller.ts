import { Controller, Post, Get, Body, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BillingService } from './billing.service';
import { MessageContent } from '../ai/ai.service';

@ApiTags('记账业务')
@Controller('billing')
export class BillingController {
  constructor(private readonly billingService: BillingService) {}

  @ApiOperation({
    summary: '处理记账请求',
    description: '处理用户的记账请求，包括AI识别和平台同步',
  })
  @Post('process')
  async processBilling(@Body() body: {
    userId: string;
    messageContent: MessageContent;
  }) {
    return await this.billingService.processBilling(body.userId, body.messageContent);
  }

  @ApiOperation({
    summary: '获取用户绑定状态',
    description: '获取用户当前的平台绑定状态',
  })
  @Get('binding-status/:userId')
  async getBindingStatus(@Param('userId') userId: string) {
    return await this.billingService.getUserBindingStatus(userId);
  }

  @ApiOperation({
    summary: '处理绑定指令',
    description: '处理用户发送的绑定相关指令',
  })
  @Post('handle-command')
  async handleCommand(@Body() body: {
    userId: string;
    command: string;
  }) {
    const response = await this.billingService.handleBindingCommand(body.userId, body.command);
    return { response };
  }
}
