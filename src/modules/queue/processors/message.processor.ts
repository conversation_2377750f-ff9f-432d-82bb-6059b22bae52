import { <PERSON>, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import {
  MessageJob,
  AiAnalysisJob,
  DataSyncJob,
  QueueService,
} from '../queue.service';
import { AiService } from '../../ai/ai.service';
import { WecomService } from '../../wecom/wecom.service';
import { NotionService } from '../../notion/notion.service';
import { FeishuService } from '../../feishu/feishu.service';
import { BindingService } from '../../binding/binding.service';
import { FileLoggerService } from '../../../common/logger/file-logger.service';

@Controller()
export class MessageProcessor {
  private readonly logger = new Logger(MessageProcessor.name);

  constructor(
    private readonly queueService: QueueService,
    private readonly wecomService: WecomService,
    private readonly fileLogger: FileLoggerService,
  ) {}

  @MessagePattern('process-message')
  async handleMessageProcessing(@Payload() payload: { data: MessageJob }) {
    const { userId, kfId, messageId, messageType, content, mediaId } =
      payload.data;

    try {
      // 记录关键业务日志到文件
      this.fileLogger.logBusiness(
        '开始处理企业微信消息',
        {
          messageId,
          messageType,
          userId,
          kfId,
          hasMediaId: !!mediaId,
        },
        'MessageProcessor',
      );

      this.logger.log(`开始处理消息: ${messageId}, 类型: ${messageType}`);
      this.queueService.updateQueueStats('message-processing', 'active');

      // 1. 预处理消息内容
      let processedContent = content;
      let mediaUrl: string | undefined;

      if (messageType === 'image' && mediaId) {
        // TODO: 下载图片并获取URL
        mediaUrl = await this.downloadMedia(mediaId, 'image');
        processedContent = `[图片消息: ${mediaUrl}]`;
      } else if (messageType === 'voice' && mediaId) {
        // TODO: 下载语音并转换为文本
        mediaUrl = await this.downloadMedia(mediaId, 'voice');
        processedContent = await this.convertVoiceToText(mediaUrl);
      } else if (messageType === 'file' && mediaId) {
        // TODO: 下载文件并提取内容
        mediaUrl = await this.downloadMedia(mediaId, 'file');
        processedContent = await this.extractFileContent(mediaUrl);
      }

      // 2. 触发AI分析任务
      const aiJob: AiAnalysisJob = {
        userId,
        kfId,
        messageId,
        messageType,
        content: processedContent,
        mediaUrl,
      };

      // 添加AI分析任务到队列
      await this.queueService.addAiAnalysisJob(aiJob);

      this.queueService.updateQueueStats('message-processing', 'completed');

      // 记录处理成功的业务日志
      this.fileLogger.logBusiness(
        '企业微信消息处理完成',
        {
          messageId,
          messageType,
          userId,
          processedContentLength: processedContent?.length || 0,
          hasMediaUrl: !!mediaUrl,
        },
        'MessageProcessor',
      );

      this.logger.log(`消息处理完成: ${messageId}`);

      return {
        success: true,
        processedContent,
        mediaUrl,
      };
    } catch (error) {
      this.queueService.updateQueueStats('message-processing', 'failed');

      // 记录处理失败的错误日志
      this.fileLogger.error(
        `企业微信消息处理失败: ${messageId}`,
        error.stack,
        'MessageProcessor',
      );

      this.logger.error(`消息处理失败: ${messageId}`, error);
      throw error;
    }
  }

  /**
   * 下载媒体文件
   */
  private async downloadMedia(mediaId: string, type: string): Promise<string> {
    try {
      this.logger.log(`下载媒体文件: ${mediaId}, 类型: ${type}`);

      // TODO: 实现企业微信媒体文件下载
      // 1. 获取access_token
      // 2. 调用媒体下载API
      // 3. 保存到本地或云存储
      // 4. 返回可访问的URL

      // 暂时返回模拟URL
      return `https://example.com/media/${mediaId}.${type === 'image' ? 'jpg' : type === 'voice' ? 'amr' : 'bin'}`;
    } catch (error) {
      this.logger.error(`下载媒体文件失败: ${mediaId}`, error);
      throw error;
    }
  }

  /**
   * 语音转文本
   */
  private async convertVoiceToText(voiceUrl: string): Promise<string> {
    try {
      this.logger.log(`语音转文本: ${voiceUrl}`);

      // TODO: 实现语音转文本
      // 1. 下载语音文件
      // 2. 调用语音识别API（如百度、阿里云等）
      // 3. 返回识别结果

      // 暂时返回模拟文本
      return '[语音内容: 午餐花了25元在麦当劳]';
    } catch (error) {
      this.logger.error(`语音转文本失败: ${voiceUrl}`, error);
      return '[语音转文本失败]';
    }
  }

  /**
   * 提取文件内容
   */
  private async extractFileContent(fileUrl: string): Promise<string> {
    try {
      this.logger.log(`提取文件内容: ${fileUrl}`);

      // TODO: 实现文件内容提取
      // 1. 下载文件
      // 2. 根据文件类型提取内容（PDF、Excel、图片OCR等）
      // 3. 返回提取的文本内容

      // 暂时返回模拟内容
      return '[文件内容: 餐饮发票，金额：25.50元]';
    } catch (error) {
      this.logger.error(`提取文件内容失败: ${fileUrl}`, error);
      return '[文件内容提取失败]';
    }
  }
}

@Controller()
export class AiAnalysisProcessor {
  private readonly logger = new Logger(AiAnalysisProcessor.name);

  constructor(
    private readonly aiService: AiService,
    private readonly queueService: QueueService,
    private readonly wecomService: WecomService,
    private readonly bindingService: BindingService,
  ) {}

  @MessagePattern('analyze-message')
  async handleAiAnalysis(@Payload() payload: { data: AiAnalysisJob }) {
    const { userId, kfId, messageId, messageType, content, mediaUrl } =
      payload.data;

    try {
      this.logger.log(`开始AI分析: ${messageId}`);
      this.queueService.updateQueueStats('ai-analysis', 'active');

      // 调用AI服务进行分析
      const billingInfo = await this.aiService.extractBillingInfoFromQueue(
        messageType,
        content,
        mediaUrl,
      );

      // 检查AI分析是否成功
      if (billingInfo.error) {
        // 发送错误消息给用户
        await this.wecomService.sendKfMessage(
          kfId,
          userId,
          `分析失败: ${billingInfo.error}`,
        );
        this.queueService.updateQueueStats('ai-analysis', 'failed');
        throw new Error(billingInfo.error);
      }

      const analysisResult = {
        amount: billingInfo.amount,
        category: billingInfo.category,
        date: billingInfo.date,
        description: billingInfo.description,
      };
      this.logger.log(`AI分析完成: ${messageId}`);

      // 获取用户绑定的平台
      const bindings = await this.bindingService.getUserBindings(userId);
      const platforms = bindings
        .filter((binding) => binding.is_active)
        .map((binding) => binding.type.toLowerCase());

      if (platforms.length === 0) {
        // 用户没有绑定任何平台，发送提示消息
        await this.wecomService.sendKfMessage(
          kfId,
          userId,
          `✅ 记账信息识别成功！\n💰 金额: ¥${analysisResult.amount}\n📂 分类: ${analysisResult.category}\n📅 日期: ${analysisResult.date}\n📝 描述: ${analysisResult.description}\n\n⚠️ 您还没有绑定Notion或飞书，请先绑定后才能自动同步数据。\n发送"绑定Notion"或"绑定飞书"开始绑定。`,
        );
      } else {
        // 触发数据同步任务
        const syncJob: DataSyncJob = {
          userId,
          billingInfo: analysisResult,
          platforms,
        };

        await this.queueService.addDataSyncJob(syncJob);

        // 发送成功消息
        await this.wecomService.sendKfMessage(
          kfId,
          userId,
          `✅ 记账信息识别成功！\n💰 金额: ¥${analysisResult.amount}\n📂 分类: ${analysisResult.category}\n📅 日期: ${analysisResult.date}\n📝 描述: ${analysisResult.description}\n\n🔄 正在同步到: ${platforms.join(', ')}...`,
        );
      }

      this.queueService.updateQueueStats('ai-analysis', 'completed');
      this.logger.log(`AI分析完成: ${messageId}`);

      return {
        success: true,
        billingInfo: analysisResult,
      };
    } catch (error) {
      this.queueService.updateQueueStats('ai-analysis', 'failed');
      this.logger.error(`AI分析失败: ${messageId}`, error);
      throw error;
    }
  }
}

@Controller()
export class DataSyncProcessor {
  private readonly logger = new Logger(DataSyncProcessor.name);

  constructor(
    private readonly notionService: NotionService,
    private readonly feishuService: FeishuService,
    private readonly bindingService: BindingService,
    private readonly wecomService: WecomService,
    private readonly queueService: QueueService,
  ) {}

  @MessagePattern('sync-data')
  async handleDataSync(@Payload() payload: { data: DataSyncJob }) {
    const { userId, billingInfo, platforms } = payload.data;

    try {
      this.logger.log(`开始数据同步: ${userId}`);
      this.queueService.updateQueueStats('data-sync', 'active');

      const results: Array<{
        platform: string;
        success: boolean;
        error?: string;
      }> = [];

      for (const platform of platforms) {
        try {
          if (platform === 'notion') {
            // TODO: 同步到Notion
            await this.syncToNotion(userId, billingInfo);
            results.push({ platform: 'notion', success: true });
          } else if (platform === 'feishu') {
            // TODO: 同步到飞书
            await this.syncToFeishu(userId, billingInfo);
            results.push({ platform: 'feishu', success: true });
          }
        } catch (error) {
          this.logger.error(`同步到${platform}失败:`, error);
          results.push({ platform, success: false, error: error.message });
        }
      }

      this.queueService.updateQueueStats('data-sync', 'completed');
      this.logger.log(`数据同步完成: ${userId}`);

      return {
        success: true,
        results,
      };
    } catch (error) {
      this.queueService.updateQueueStats('data-sync', 'failed');
      this.logger.error(`数据同步失败: ${userId}`, error);
      throw error;
    }
  }

  private async syncToNotion(userId: string, billingInfo: any): Promise<void> {
    try {
      // 获取用户的Notion绑定信息
      const binding = await this.bindingService.getNotionBinding(userId);
      if (!binding) {
        throw new Error('未找到Notion绑定信息');
      }

      // 同步到Notion数据库
      await this.notionService.addRecordToDatabase(
        binding.access_token,
        binding.target_id,
        billingInfo,
      );

      this.logger.log(`成功同步到Notion: ${userId}`);
    } catch (error) {
      this.logger.error(`同步到Notion失败: ${userId}`, error);
      throw error;
    }
  }

  private async syncToFeishu(userId: string, billingInfo: any): Promise<void> {
    try {
      // 获取用户的飞书绑定信息
      const binding = await this.bindingService.getFeishuBinding(userId);
      if (!binding) {
        throw new Error('未找到飞书绑定信息');
      }

      // 同步到飞书多维表格
      await this.feishuService.addRecordToTable(
        binding.access_token,
        binding.target_id,
        'default_view', // 视图ID，暂时使用默认值
        billingInfo,
      );

      this.logger.log(`成功同步到飞书: ${userId}`);
    } catch (error) {
      this.logger.error(`同步到飞书失败: ${userId}`, error);
      throw error;
    }
  }
}
