// 数据源配置
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 生成Prisma Client配置
generator client {
  provider = "prisma-client-js"
}

// 用户表模型
model User {
  id         String      @id @db.VarChar(255) // 企业微信ExternalUserID
  name       String?     @db.VarChar(255)     // 用户姓名
  state      String?     @db.VarChar(50)      // 用户状态
  usageCount Int         @default(0)          // 使用次数
  created_at DateTime    @default(now()) @db.Timestamp(6)
  updated_at DateTime    @updatedAt @db.Timestamp(6)
  
  // 关系定义
  bindings      Binding[]
  subscriptions Subscription[]
  usageStats    UsageStat[]
}

// 绑定关系表模型
model Binding {
  id           String      @id @default(uuid()) @db.VarChar(36)
  user_id      String      @db.VarChar(255)
  type         BindingType
  access_token  String      @db.Text
  refresh_token String?     @db.Text
  target_id     String      @db.VarChar(255)
  target_name   String?     @db.VarChar(255)
  is_active    Boolean     @default(false)
  created_at   DateTime    @default(now()) @db.Timestamp(6)
  updated_at   DateTime    @updatedAt @db.Timestamp(6)

  user         User        @relation(fields: [user_id], references: [id])

  @@map("bindings")
}

// 订阅表模型
model Subscription {
  id         String   @id @default(uuid()) @db.VarChar(36)
  user_id    String   @db.VarChar(255)
  plan       String   @db.VarChar(50) // monthly, yearly
  limit      Int      @default(1000)  // 订阅限额
  start_date DateTime @db.Timestamp(6)
  end_date   DateTime @db.Timestamp(6)
  status     String   @db.VarChar(50) // active, canceled, expired
  created_at DateTime @default(now()) @db.Timestamp(6)
  
  // 关系定义
  user User @relation(fields: [user_id], references: [id])
}

enum BindingType {
  NOTION
  FEISHU
}

// 用量统计表模型
model UsageStat {
  id         String   @id @default(uuid()) @db.VarChar(36)
  user_id    String   @db.VarChar(255)
  year_month Int      // 统计年月，格式为YYYYMM
  count      Int      @default(0) // 当月已用条目数
  updated_at DateTime @updatedAt @db.Timestamp(6)

  // 关系定义
  user User @relation(fields: [user_id], references: [id])

  // 复合唯一索引
  @@unique([user_id, year_month], name: "user_id_year_month")
}

// 企微同步游标表模型
model WecomSyncCursor {
  id         String   @id @db.VarChar(255) // 可为'global_cursor'
  cursor     String   @db.VarChar(255) // 上次同步的游标
  updated_at DateTime @updatedAt @db.Timestamp(6)
}
