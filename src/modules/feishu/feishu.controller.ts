import { Controller, Get, Post, Body, Param, Query, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { FeishuService, FeishuAuthToken } from './feishu.service';

@ApiTags('飞书集成')
@Controller('feishu')
export class FeishuController {
  constructor(private readonly feishuService: FeishuService) {}

  @ApiOperation({
    summary: '获取飞书OAuth授权URL',
    description: '生成飞书OAuth授权链接',
  })
  @Get('auth-url')
  getAuthUrl(@Query('userId') userId: string) {
    const authUrl = this.feishuService.getAuthUrl(userId);
    return { authUrl };
  }

  @ApiOperation({
    summary: '处理飞书OAuth回调',
    description: '处理飞书OAuth授权回调',
  })
  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('state') userId: string,
    @Res() res: Response,
  ) {
    try {
      if (!code) {
        return res.status(400).send('Missing authorization code');
      }

      const tokenResponse = await this.feishuService.handleAuthCallback(code);

      if (userId) {
        // 获取用户可访问的应用
        const apps = await this.feishuService.getUserApps(tokenResponse.access_token);

        // 重定向到应用选择页面
        const params = new URLSearchParams({
          access_token: tokenResponse.access_token,
          refresh_token: tokenResponse.refresh_token,
          userId,
          apps: JSON.stringify(apps),
        });

        return res.redirect(`/feishu/select-app?${params.toString()}`);
      }

      return res.json(tokenResponse);
    } catch (error) {
      return res.status(500).send('Authorization failed');
    }
  }

  @ApiOperation({
    summary: '获取用户应用列表',
    description: '获取用户可访问的飞书应用列表',
  })
  @Post('apps')
  async getApps(@Body() body: { accessToken: string }) {
    return await this.feishuService.getUserApps(body.accessToken);
  }

  @ApiOperation({
    summary: '获取应用表格列表',
    description: '获取指定应用的表格列表',
  })
  @Post('tables')
  async getTables(@Body() body: { accessToken: string; appToken: string }) {
    return await this.feishuService.getAppTables(body.accessToken, body.appToken);
  }

  @ApiOperation({
    summary: '创建绑定',
    description: '创建用户与飞书表格的绑定关系',
  })
  @Post('bind')
  async createBinding(@Body() body: {
    userId: string;
    accessToken: string;
    refreshToken: string;
    appToken: string;
    tableId: string;
    tableName?: string;
  }) {
    await this.feishuService.createBinding({
      userId: body.userId,
      accessToken: body.accessToken,
      refreshToken: body.refreshToken,
      appToken: body.appToken,
      tableId: `${body.appToken}:${body.tableId}`, // 组合存储
      tableName: body.tableName,
    });
    return { success: true };
  }

  @ApiOperation({
    summary: '获取用户绑定信息',
    description: '获取用户的活跃飞书绑定信息',
  })
  @Get('binding/:userId')
  async getBinding(@Param('userId') userId: string) {
    const binding = await this.feishuService.getActiveBinding(userId);
    if (!binding) {
      return { bound: false };
    }

    const [appToken, tableId] = binding.target_id.split(':');
    return {
      bound: true,
      appToken,
      tableId,
      tableName: binding.target_name,
    };
  }
}
