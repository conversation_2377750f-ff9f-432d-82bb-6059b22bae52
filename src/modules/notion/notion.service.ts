import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { Client } from '@notionhq/client';
import { PrismaService } from '../../prisma/prisma.service';
import { BillingInfo } from '../ai/ai.service';
import { encryptData, decryptData } from '../../utils/crypto.util';

/**
 * Notion OAuth 返回的Token数据结构
 */
export interface NotionTokenResponse {
  access_token: string;
  token_type: string;
  bot_id: string;
  workspace_name?: string;
  workspace_icon?: string;
  workspace_id?: string;
  owner?: {
    type: string;
    user?: {
      object: string;
      id: string;
    };
  };
}

/**
 * Notion数据库信息
 */
export interface NotionDatabase {
  id: string;
  title: string;
  properties: Record<string, any>;
}

/**
 * 绑定创建DTO
 */
export interface CreateNotionBindingDto {
  userId: string;
  accessToken: string;
  databaseId: string;
  databaseName?: string;
}

@Injectable()
export class NotionService {
  private readonly logger = new Logger(NotionService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly prisma: PrismaService,
  ) {}

  /**
   * 获取Notion OAuth授权URL
   */
  getAuthUrl(state?: string): string {
    const clientId = this.configService.get<string>('NOTION_CLIENT_ID');
    const redirectUri = this.configService.get<string>('NOTION_REDIRECT_URI');

    if (!clientId || !redirectUri) {
      throw new Error('Missing Notion OAuth configuration');
    }

    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      owner: 'user', // 指定为用户授权
    });

    if (state) {
      params.append('state', state);
    }

    return `https://api.notion.com/v1/oauth/authorize?${params.toString()}`;
  }

  /**
   * 处理Notion OAuth回调
   * @param code 授权码
   * @returns 返回access_token和相关信息
   */
  async handleAuthCallback(code: string): Promise<NotionTokenResponse> {
    const clientId = this.configService.get<string>('NOTION_CLIENT_ID');
    const clientSecret = this.configService.get<string>('NOTION_CLIENT_SECRET');
    const redirectUri = this.configService.get<string>('NOTION_REDIRECT_URI');

    if (!clientId || !clientSecret || !redirectUri) {
      throw new Error('Missing Notion OAuth configuration');
    }

    try {
      // 使用Basic Auth进行token交换
      const credentials = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

      const { data } = await firstValueFrom(
        this.httpService.post<NotionTokenResponse>(
          'https://api.notion.com/v1/oauth/token',
          {
            grant_type: 'authorization_code',
            code,
            redirect_uri: redirectUri,
          },
          {
            headers: {
              'Authorization': `Basic ${credentials}`,
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Notion-Version': '2022-06-28', // 指定Notion API版本
            },
          },
        ),
      );

      this.logger.log('Notion OAuth成功获取访问令牌');
      return data;
    } catch (error: any) {
      this.logger.error('Notion OAuth错误:', error.response?.data || error.message);
      throw new Error(`Failed to exchange code for access token: ${error.response?.data?.error || error.message}`);
    }
  }

  /**
   * 获取用户可访问的数据库列表
   */
  async getUserDatabases(accessToken: string): Promise<NotionDatabase[]> {
    const notion = new Client({
      auth: accessToken,
      notionVersion: '2022-06-28'
    });

    try {
      const response = await notion.search({
        filter: {
          value: 'database',
          property: 'object',
        },
        page_size: 100, // 获取更多数据库
      });

      return response.results.map((db: any) => ({
        id: db.id,
        title: this.extractDatabaseTitle(db),
        properties: db.properties,
      }));
    } catch (error) {
      this.logger.error('获取Notion数据库列表失败:', error);
      throw new Error('Failed to fetch databases');
    }
  }

  /**
   * 提取数据库标题
   */
  private extractDatabaseTitle(database: any): string {
    if (database.title && database.title.length > 0) {
      return database.title[0].plain_text || 'Untitled Database';
    }
    return 'Untitled Database';
  }

  /**
   * 创建绑定关系
   */
  async createBinding(dto: CreateNotionBindingDto): Promise<void> {
    const encryptedToken = encryptData(dto.accessToken);

    await this.prisma.binding.create({
      data: {
        user_id: dto.userId,
        type: 'NOTION',
        access_token: encryptedToken,
        target_id: dto.databaseId,
        target_name: dto.databaseName,
        is_active: true,
      },
    });

    // 将其他Notion绑定设为非活跃
    await this.prisma.binding.updateMany({
      where: {
        user_id: dto.userId,
        type: 'NOTION',
        target_id: { not: dto.databaseId },
      },
      data: {
        is_active: false,
      },
    });
  }

  /**
   * 将一条记账记录添加到 Notion Database
   */
  async addRecordToDatabase(
    accessToken: string,
    databaseId: string,
    data: BillingInfo,
  ): Promise<any> {
    const notion = new Client({
      auth: accessToken,
      notionVersion: '2022-06-28'
    });

    try {
      // 首先获取数据库结构以了解属性名称
      const database = await notion.databases.retrieve({
        database_id: databaseId,
      });

      // 动态构建属性对象
      const properties: any = {};

      // 查找合适的属性来映射数据
      const dbProperties = (database as any).properties;

      // 标记是否找到了必要的属性
      let hasTitle = false;

      for (const [propName, propConfig] of Object.entries(dbProperties)) {
        const config = propConfig as any;
        const lowerPropName = propName.toLowerCase();

        // 映射描述到title类型的属性（必须有一个title属性）
        if (config.type === 'title' && !hasTitle) {
          properties[propName] = {
            title: [
              {
                text: {
                  content: data.description || '记账记录',
                },
              },
            ],
          };
          hasTitle = true;
        }

        // 映射金额到number类型的属性
        if (config.type === 'number' &&
            (lowerPropName.includes('金额') ||
             lowerPropName.includes('amount') ||
             lowerPropName.includes('价格') ||
             lowerPropName.includes('费用'))) {
          properties[propName] = {
            number: data.amount,
          };
        }

        // 映射分类到select类型的属性
        if (config.type === 'select' &&
            (lowerPropName.includes('分类') ||
             lowerPropName.includes('category') ||
             lowerPropName.includes('类型') ||
             lowerPropName.includes('type'))) {
          // 检查选项是否存在，如果不存在则创建
          const existingOptions = config.select?.options || [];
          const optionExists = existingOptions.some((option: any) => option.name === data.category);

          properties[propName] = {
            select: {
              name: data.category,
            },
          };
        }

        // 映射日期到date类型的属性
        if (config.type === 'date' &&
            (lowerPropName.includes('日期') ||
             lowerPropName.includes('date') ||
             lowerPropName.includes('时间') ||
             lowerPropName.includes('time'))) {
          properties[propName] = {
            date: {
              start: data.date,
            },
          };
        }

        // 映射置信度到number类型的属性
        if (config.type === 'number' &&
            (lowerPropName.includes('置信度') ||
             lowerPropName.includes('confidence'))) {
          properties[propName] = {
            number: data.confidence || 0,
          };
        }
      }

      // 如果没有找到title属性，使用第一个title属性
      if (!hasTitle) {
        const titleProp = Object.entries(dbProperties).find(([_, config]) => (config as any).type === 'title');
        if (titleProp) {
          properties[titleProp[0]] = {
            title: [
              {
                text: {
                  content: data.description || '记账记录',
                },
              },
            ],
          };
        }
      }

      const response = await notion.pages.create({
        parent: { database_id: databaseId },
        properties,
      });

      this.logger.log('成功添加记录到Notion数据库');
      return response;
    } catch (error) {
      this.logger.error('添加记录到Notion失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的活跃Notion绑定
   */
  async getActiveBinding(userId: string) {
    const binding = await this.prisma.binding.findFirst({
      where: {
        user_id: userId,
        type: 'NOTION',
        is_active: true,
      },
    });

    if (!binding) {
      return null;
    }

    return {
      ...binding,
      access_token: decryptData(binding.access_token),
    };
  }

  /**
   * 同步记账数据到Notion
   */
  async syncBillingData(userId: string, billingInfo: BillingInfo): Promise<void> {
    const binding = await this.getActiveBinding(userId);

    if (!binding) {
      throw new Error('用户未绑定Notion账户');
    }

    await this.addRecordToDatabase(
      binding.access_token,
      binding.target_id,
      billingInfo,
    );
  }
}
