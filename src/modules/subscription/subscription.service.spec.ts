import { Test, TestingModule } from '@nestjs/testing';
import { SubscriptionService } from './subscription.service';
import { PrismaService } from '../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';

describe('SubscriptionService', () => {
  let service: SubscriptionService;
  let prismaService: jest.Mocked<PrismaService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionService,
        {
          provide: PrismaService,
          useValue: {
            subscription: {
              create: jest.fn(),
              findFirst: jest.fn(),
              findMany: jest.fn(),
              updateMany: jest.fn(),
            },
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SubscriptionService>(SubscriptionService);
    prismaService = module.get(PrismaService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSubscription', () => {
    it('should create monthly subscription', async () => {
      // Arrange
      const dto = { userId: 'user1', plan: 'monthly' as const };
      const mockSubscription = {
        id: 'sub1',
        user_id: 'user1',
        plan: 'monthly',
        limit: 1000,
        start_date: new Date(),
        end_date: new Date(),
        status: 'active',
      };

      prismaService.subscription.updateMany.mockResolvedValue({ count: 0 });
      prismaService.subscription.create.mockResolvedValue(mockSubscription);

      // Act
      const result = await service.createSubscription(dto);

      // Assert
      expect(result.plan).toBe('monthly');
      expect(result.limit).toBe(1000);
      expect(result.isActive).toBe(true);
      expect(prismaService.subscription.updateMany).toHaveBeenCalled();
    });

    it('should create yearly subscription', async () => {
      // Arrange
      const dto = { userId: 'user1', plan: 'yearly' as const };
      const mockSubscription = {
        id: 'sub1',
        user_id: 'user1',
        plan: 'yearly',
        limit: 12000,
        start_date: new Date(),
        end_date: new Date(),
        status: 'active',
      };

      prismaService.subscription.updateMany.mockResolvedValue({ count: 0 });
      prismaService.subscription.create.mockResolvedValue(mockSubscription);

      // Act
      const result = await service.createSubscription(dto);

      // Assert
      expect(result.plan).toBe('yearly');
      expect(result.limit).toBe(12000);
    });
  });

  describe('getCurrentSubscription', () => {
    it('should return current active subscription', async () => {
      // Arrange
      const userId = 'user1';
      const mockSubscription = {
        id: 'sub1',
        user_id: userId,
        plan: 'monthly',
        limit: 1000,
        start_date: new Date(),
        end_date: new Date(Date.now() + 86400000), // tomorrow
        status: 'active',
      };

      prismaService.subscription.findFirst.mockResolvedValue(mockSubscription);

      // Act
      const result = await service.getCurrentSubscription(userId);

      // Assert
      expect(result).toBeDefined();
      expect(result!.id).toBe('sub1');
      expect(result!.isActive).toBe(true);
    });

    it('should return null when no active subscription', async () => {
      // Arrange
      const userId = 'user1';
      prismaService.subscription.findFirst.mockResolvedValue(null);

      // Act
      const result = await service.getCurrentSubscription(userId);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('hasValidSubscription', () => {
    it('should return true for valid subscription', async () => {
      // Arrange
      const userId = 'user1';
      jest.spyOn(service, 'getCurrentSubscription').mockResolvedValue({
        id: 'sub1',
        plan: 'monthly',
        limit: 1000,
        startDate: new Date(),
        endDate: new Date(Date.now() + 86400000),
        status: 'active',
        isActive: true,
      });

      // Act
      const result = await service.hasValidSubscription(userId);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false for no subscription', async () => {
      // Arrange
      const userId = 'user1';
      jest.spyOn(service, 'getCurrentSubscription').mockResolvedValue(null);

      // Act
      const result = await service.hasValidSubscription(userId);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('getUserLimit', () => {
    it('should return subscription limit', async () => {
      // Arrange
      const userId = 'user1';
      jest.spyOn(service, 'getCurrentSubscription').mockResolvedValue({
        id: 'sub1',
        plan: 'monthly',
        limit: 1000,
        startDate: new Date(),
        endDate: new Date(Date.now() + 86400000),
        status: 'active',
        isActive: true,
      });

      // Act
      const result = await service.getUserLimit(userId);

      // Assert
      expect(result).toBe(1000);
    });

    it('should return free limit when no subscription', async () => {
      // Arrange
      const userId = 'user1';
      jest.spyOn(service, 'getCurrentSubscription').mockResolvedValue(null);
      configService.get.mockReturnValue(30);

      // Act
      const result = await service.getUserLimit(userId);

      // Assert
      expect(result).toBe(30);
    });
  });

  describe('cancelSubscription', () => {
    it('should cancel active subscriptions', async () => {
      // Arrange
      const userId = 'user1';
      prismaService.subscription.updateMany.mockResolvedValue({ count: 1 });

      // Act
      await service.cancelSubscription(userId);

      // Assert
      expect(prismaService.subscription.updateMany).toHaveBeenCalledWith({
        where: {
          user_id: userId,
          status: 'active',
        },
        data: {
          status: 'canceled',
        },
      });
    });
  });
});
