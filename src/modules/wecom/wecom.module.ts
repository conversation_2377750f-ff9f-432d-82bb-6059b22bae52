import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { WecomController } from './wecom.controller';
import { WecomService } from './wecom.service';
import { UserModule } from '../user/user.module';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    HttpModule,
    UserModule,
    PrismaModule,
    forwardRef(() => import('../queue/queue.module').then(m => m.QueueModule)),
  ],
  controllers: [WecomController],
  providers: [WecomService],
  exports: [WecomService],
})
export class WecomModule {}
