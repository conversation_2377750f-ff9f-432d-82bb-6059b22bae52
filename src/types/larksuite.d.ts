declare module '@larksuiteoapi/api' {
  interface ClientOptions {
    appId: string;
    appSecret: string;
    domain: string;
  }

  interface DocumentAppendContentParams {
    document_id: string;
    content: string;
    content_type: string;
  }

  interface DocumentResult {
    // 根据实际API响应定义
    [key: string]: any;
  }

  class Client {
    constructor(options: ClientOptions);
    documents: {
      appendContent(
        params: DocumentAppendContentParams,
      ): Promise<DocumentResult>;
    };
  }

  export { Client };
}
