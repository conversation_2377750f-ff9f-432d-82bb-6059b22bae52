import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Delete,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { BindingService } from './binding.service';
import { CreateBindingDto } from './dto/create-binding.dto';

@Controller('bindings')
export class BindingController {
  constructor(private readonly bindingService: BindingService) {}

  @Post()
  create(@Body() createBindingDto: CreateBindingDto, @Req() req: Request) {
    // 假设认证守卫已将用户信息附加到请求中。
    // 我们已经通过 express.d.ts 扩展了 Request 类型，因此可以直接访问 req.user。
    const userId = req.user?.id;

    // 增加一个保护，如果因为某种原因未能获取到用户ID，则抛出异常。
    if (!userId) {
      throw new UnauthorizedException('无法识别用户身份，请重新登录。');
    }

    return this.bindingService.create({ ...createBindingDto, user_id: userId });
  }

  @Get('user/:userId')
  findByUser(@Param('userId') userId: string) {
    return this.bindingService.findByUser(userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.bindingService.remove(id);
  }
}
