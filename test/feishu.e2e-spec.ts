import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from '@larksuiteoapi/api';
import { FeishuModule } from '../src/modules/feishu/feishu.module';
import { FeishuService } from '../src/modules/feishu/feishu.service';

// Mock 飞书客户端
jest.mock('@larksuiteoapi/api');
const mockAppendContent = jest.fn();

describe('FeishuService (e2e)', () => {
  let app: INestApplication;
  let feishuService: FeishuService;

  const testData = {
    documentId: '测试文档ID',
    billingRecord: {
      amount: 100,
      category: '餐饮',
      date: '2023-07-10',
      description: '午餐消费',
    },
  };

  beforeAll(async () => {
    // Mock Client 实现
    (Client as jest.Mock).mockImplementation(() => ({
      documents: {
        appendContent: mockAppendContent,
      },
    }));

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [FeishuModule],
    })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn((key: string) => {
          if (key === 'FEISHU_APP_ID') return 'mock-app-id';
          if (key === 'FEISHU_APP_SECRET') return 'mock-app-secret';
          return null;
        }),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    feishuService = app.get(FeishuService);
    await app.init();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('addRecordToDocument', () => {
    it('成功向文档追加记录', async () => {
      mockAppendContent.mockResolvedValueOnce({});

      await expect(
        feishuService.addRecordToDocument(
          testData.documentId,
          testData.billingRecord,
        ),
      ).resolves.not.toThrow();

      expect(mockAppendContent).toHaveBeenCalledWith({
        document_id: testData.documentId,
        content: expect.stringContaining('午餐消费') as string,
        content_type: 'markdown',
      });
    });

    it('处理文档不存在错误', async () => {
      mockAppendContent.mockRejectedValueOnce(
        new Error('文档不存在或无权访问'),
      );

      await expect(
        feishuService.addRecordToDocument(
          'invalid-doc-id',
          testData.billingRecord,
        ),
      ).rejects.toThrow('文档不存在或无权访问');
    });

    it('处理无效凭证错误', async () => {
      mockAppendContent.mockRejectedValueOnce(new Error('无效的APP凭证'));

      await expect(
        feishuService.addRecordToDocument(
          testData.documentId,
          testData.billingRecord,
        ),
      ).rejects.toThrow('无效的APP凭证');
    });
  });
});
