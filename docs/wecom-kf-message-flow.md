# 企业微信客服消息处理流程

## 概述

FlashBookkeeping 使用企业微信的客服消息API来接收和处理用户的记账消息。整个流程包括URL验证、消息事件接收、增量消息拉取、AI分析和数据同步。

## 架构流程图

```
用户发送消息 → 企业微信服务器 → 推送事件到回调URL → 解密获取Token/Cursor → 
拉取增量消息 → 消息队列 → AI分析 → 格式化输出 → 同步到Notion/飞书 → 发送回复给用户
```

## 详细流程

### 1. URL验证阶段

**端点**: `GET /wecom/callback`

**用途**: 配置企业微信客服消息回调地址时的验证

**参数**:
- `msg_signature`: 消息签名
- `timestamp`: 时间戳
- `nonce`: 随机数
- `echostr`: 加密的验证字符串

**处理逻辑**:
1. 验证签名是否正确
2. 解密 `echostr` 获取明文
3. 返回解密后的明文

**代码示例**:
```typescript
// 验证签名
const signature = getSignature(this.token, timestamp, nonce, echostr);
if (signature !== msgSignature) {
  throw new Error('签名验证失败');
}

// 解密echostr
const { message } = decrypt(this.encodingAESKey, echostr);
return message;
```

### 2. 消息事件接收阶段

**端点**: `POST /wecom/callback`

**用途**: 接收企业微信推送的客服消息事件

**参数**:
- `msg_signature`: 消息签名
- `timestamp`: 时间戳
- `nonce`: 随机数
- `body`: 加密的XML数据

**处理逻辑**:
1. 验证消息签名
2. 解密XML数据获取事件信息
3. 解析XML获取Token和Cursor
4. 调用增量消息拉取

**XML事件格式**:
```xml
<xml>
    <ToUserName><![CDATA[企业微信CorpID]]></ToUserName>
    <FromUserName><![CDATA[sys]]></FromUserName>
    <CreateTime>1234567890</CreateTime>
    <MsgType><![CDATA[event]]></MsgType>
    <Event><![CDATA[kf_msg_or_event]]></Event>
    <InfoType><![CDATA[kf_msg_or_event]]></InfoType>
    <Token><![CDATA[拉取消息的token]]></Token>
    <Cursor><![CDATA[拉取消息的游标]]></Cursor>
</xml>
```

### 3. 增量消息拉取阶段

**API**: `POST https://qyapi.weixin.qq.com/cgi-bin/kf/sync_msg`

**用途**: 使用Token和Cursor拉取用户发送的具体消息

**请求参数**:
```json
{
  "cursor": "从事件中获取的cursor",
  "token": "从事件中获取的token", 
  "limit": 1000
}
```

**响应格式**:
```json
{
  "errcode": 0,
  "errmsg": "ok",
  "next_cursor": "下一页游标",
  "msg_list": [
    {
      "msgid": "消息ID",
      "open_kfid": "客服ID", 
      "external_userid": "外部用户ID",
      "send_time": 1234567890,
      "origin": 3,
      "msgtype": "text",
      "text": {
        "content": "用户发送的文本内容"
      }
    }
  ]
}
```

**处理逻辑**:
1. 使用access_token调用API
2. 遍历msg_list处理每条消息
3. 只处理origin=3的用户消息
4. 如果有next_cursor，继续拉取

### 4. 消息处理阶段

**支持的消息类型**:
- `text`: 文本消息
- `image`: 图片消息（需下载media_id对应的图片）
- `voice`: 语音消息（需下载media_id对应的语音）
- `file`: 文件消息（需下载media_id对应的文件）

**处理流程**:
1. 确保用户存在（自动创建用户记录）
2. 发送"处理中"提示消息
3. 根据消息类型提取内容
4. 发送到消息队列进行AI分析
5. 发送处理结果给用户

### 5. AI分析阶段（TODO）

**流程**:
1. 从消息队列接收消息
2. 调用多模态LLM分析内容
3. 提取记账信息（金额、类别、日期、描述等）
4. 验证和格式化数据
5. 返回结构化的记账信息

**预期输出格式**:
```json
{
  "amount": 25.50,
  "category": "餐饮",
  "date": "2025-07-12",
  "description": "午餐 - 麦当劳",
  "confidence": 0.95
}
```

### 6. 数据同步阶段（TODO）

**流程**:
1. 检查用户绑定状态
2. 同步到Notion数据库
3. 同步到飞书多维表格
4. 记录同步结果

### 7. 用户反馈阶段

**API**: `POST https://qyapi.weixin.qq.com/cgi-bin/kf/send_msg`

**用途**: 向用户发送处理结果

**请求格式**:
```json
{
  "touser": "外部用户ID",
  "open_kfid": "客服ID",
  "msgtype": "text",
  "text": {
    "content": "记账成功！金额：¥25.50，类别：餐饮"
  }
}
```

## 配置要求

### 环境变量

```bash
# 企业微信配置
WECOM_CORP_ID="企业ID"
WECOM_TOKEN="回调验证Token"
WECOM_ENCODING_AES_KEY="消息加密密钥"
WECOM_SECRET="应用密钥"
```

### 企业微信后台配置

1. **创建客服应用**
   - 在企业微信管理后台创建客服应用
   - 获取应用的Secret

2. **配置回调URL**
   - URL: `https://yourdomain.com/wecom/callback`
   - Token: 随机字符串
   - EncodingAESKey: 43位随机字符串

3. **设置可信域名**
   - 将应用域名添加到可信域名列表

## 错误处理

### 常见错误

1. **签名验证失败**
   - 检查Token配置是否正确
   - 确认时间戳和随机数参数

2. **解密失败**
   - 检查EncodingAESKey配置
   - 确认CorpID是否正确

3. **拉取消息失败**
   - 检查access_token是否有效
   - 确认应用权限配置

4. **发送消息失败**
   - 检查客服ID是否正确
   - 确认用户是否在会话中

### 日志记录

系统会记录以下关键日志：
- URL验证请求和结果
- 消息事件接收和解密
- 增量消息拉取结果
- 消息处理状态
- 发送消息结果

## 测试方法

### 1. URL验证测试

```bash
curl "http://localhost:3000/wecom/callback?msg_signature=test&timestamp=123456&nonce=test&echostr=test"
```

### 2. 消息回调测试

```bash
curl -X POST "http://localhost:3000/wecom/callback?msg_signature=test&timestamp=123456&nonce=test" \
  -H "Content-Type: application/xml" \
  -d "<xml>...</xml>"
```

## 安全注意事项

1. **签名验证**: 必须验证每个请求的签名
2. **HTTPS**: 生产环境必须使用HTTPS
3. **Token保护**: 妥善保管Token和EncodingAESKey
4. **访问控制**: 限制回调URL的访问来源
5. **日志脱敏**: 避免在日志中记录敏感信息

## 性能优化

1. **消息队列**: 使用RabbitMQ处理消息（已从Redis迁移）
2. **批量处理**: 批量拉取和处理消息
3. **缓存**: 缓存access_token避免频繁获取
4. **异步处理**: 异步处理AI分析和数据同步
5. **限流**: 控制API调用频率

## 监控指标

- 消息接收成功率
- 消息处理延迟
- AI分析准确率
- 数据同步成功率
- 用户活跃度

## 下一步开发

1. ✅ 实现消息队列集成（RabbitMQ）
2. 完善AI分析模块
3. 实现多媒体消息处理
4. 添加用户指令处理
5. 完善错误重试机制
