/* eslint-disable @typescript-eslint/no-unsafe-call */
// /src/modules/wecom/dto/wecom-message.dto.ts

/**
 * @file 企业微信消息体 DTO
 * @description 用于验证和类型约束从企业微信接收的回调消息。
 */

import { IsString, IsNotEmpty, IsOptional, IsNumber } from 'class-validator';

/**
 * @class WecomMessageDto
 * @description 定义了企业微信回调消息的基本结构。
 * 包含了解密后消息体的核心字段。
 */
export class WecomMessageDto {
  /**
   * @property {string} ToUserName - 企业微信 CorpID
   */
  @IsString()
  @IsNotEmpty()
  ToUserName: string;

  /**
   * @property {string} FromUserName - 发送消息的成员UserID
   */
  @IsString()
  @IsNotEmpty()
  FromUserName: string;

  /**
   * @property {number} CreateTime - 消息创建时间戳
   */
  @IsNumber()
  @IsNotEmpty()
  CreateTime: number;

  /**
   * @property {string} MsgType - 消息类型 (例如: 'text', 'event')
   */
  @IsString()
  @IsNotEmpty()
  MsgType: string;

  /**
   * @property {string} [Content] - 文本消息内容
   */
  @IsString()
  @IsOptional()
  Content?: string;

  /**
   * @property {number} MsgId - 消息ID
   */
  @IsNumber()
  @IsNotEmpty()
  MsgId: number;

  /**
   * @property {number} AgentID - 企业应用的AgentID
   */
  @IsNumber()
  @IsNotEmpty()
  AgentID: number;

  /**
   * @property {string} [Event] - 事件类型 (当 MsgType 为 'event' 时存在)
   */
  @IsString()
  @IsOptional()
  Event?: string;
}
