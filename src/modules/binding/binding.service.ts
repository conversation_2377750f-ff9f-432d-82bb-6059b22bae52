import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateBindingDto } from './dto/create-binding.dto';
import { Binding } from '@prisma/client';

@Injectable()
export class BindingService {
  constructor(private prisma: PrismaService) {}

  /**
   * 创建一个新的绑定记录
   * @param createBindingDto 创建绑定所需的数据
   * @returns 创建的绑定对象
   */
  async create(
    createBindingDto: CreateBindingDto & { user_id: string },
  ): Promise<Binding> {
    const {
      type,
      access_token,
      target_id,
      target_name,
      is_active = false,
      user_id,
    } = createBindingDto;
    // 根据错误提示，我们必须使用 snake_case 字段名和关联写入
    return await this.prisma.binding.create({
      data: {
        type,
        access_token,
        target_id,
        target_name,
        is_active,
        user_id,
      },
    });
  }

  /**
   * 根据用户ID查找所有绑定记录
   * @param userId 用户ID
   * @returns 用户的绑定记录数组
   */
  async findByUser(userId: string): Promise<Binding[]> {
    // 根据错误提示，我们必须使用 user_id 进行查询
    return await this.prisma.binding.findMany({
      where: { user_id: userId },
    });
  }

  /**
   * 根据ID删除一个绑定记录
   * @param id 绑定ID
   * @returns 被删除的绑定对象
   */
  async remove(id: string): Promise<Binding> {
    return await this.prisma.binding.delete({
      where: { id },
    });
  }

  /**
   * 创建Notion绑定关系
   * @param userId 用户ID
   * @param accessToken Notion访问令牌
   * @param targetId 目标ID(可选)
   * @param targetName 目标名称(可选)
   * @returns 创建的绑定对象
   */
  async createNotionBinding(
    userId: string,
    accessToken: string,
    targetId?: string,
    targetName?: string,
  ): Promise<Binding> {
    return await this.prisma.binding.create({
      data: {
        type: 'NOTION',
        access_token: accessToken, // 注意：后续需要加密
        target_id: targetId || 'default',
        target_name: targetName,
        is_active: true,
        user_id: userId,
      },
    });
  }

  /**
   * 创建飞书绑定关系
   * @param userId 用户ID
   * @param accessToken 飞书访问令牌
   * @param refreshToken 飞书刷新令牌
   * @returns 创建的绑定对象
   */
  async createFeishuBinding(
    userId: string,
    accessToken: string,
    refreshToken: string,
  ): Promise<Binding> {
    return await this.prisma.binding.create({
      data: {
        type: 'FEISHU',
        access_token: accessToken, // 注意：后续需要加密
        refresh_token: refreshToken, // 注意：后续需要加密
        is_active: true,
        user_id: userId,
        target_id: 'default', // 临时值
      },
    });
  }

  /**
   * 获取用户的所有绑定
   */
  async getUserBindings(userId: string): Promise<Binding[]> {
    return await this.prisma.binding.findMany({
      where: {
        user_id: userId,
      },
    });
  }

  /**
   * 获取用户的Notion绑定
   */
  async getNotionBinding(userId: string): Promise<Binding | null> {
    return await this.prisma.binding.findFirst({
      where: {
        user_id: userId,
        type: 'NOTION',
        is_active: true,
      },
    });
  }

  /**
   * 获取用户的飞书绑定
   */
  async getFeishuBinding(userId: string): Promise<Binding | null> {
    return await this.prisma.binding.findFirst({
      where: {
        user_id: userId,
        type: 'FEISHU',
        is_active: true,
      },
    });
  }
}
