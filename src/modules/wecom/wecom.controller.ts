import {
  Controller,
  Post,
  Body,
  Logger,
  Get,
  Query,
  Res,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { WecomService } from './wecom.service';

interface ExtendedRequest extends Request {
  rawBody?: string;
}

@ApiTags('企业微信客服消息')
@Controller('wecom')
export class WecomController {
  private readonly logger = new Logger(WecomController.name);

  constructor(private readonly wecomService: WecomService) {}

  @ApiOperation({
    summary: '验证企业微信回调URL',
    description: '用于配置企业微信客服消息回调地址时的URL验证',
  })
  @ApiResponse({ status: 200, description: '返回解密后的echostr' })
  @Get('callback')
  verifyUrl(
    @Query('msg_signature') msgSignature: string,
    @Query('timestamp') timestamp: string,
    @Query('nonce') nonce: string,
    @Query('echostr') echostr: string,
    @Res() res: Response,
  ) {
    try {
      this.logger.log('收到URL验证请求', { msgSignature, timestamp, nonce });

      const result = this.wecomService.verifyUrl(
        msgSignature,
        timestamp,
        nonce,
        echostr,
      );

      this.logger.log('URL验证成功');
      return res.send(result);
    } catch (error) {
      this.logger.error('URL验证失败:', error);
      return res.status(400).send('verification failed');
    }
  }

  @ApiOperation({
    summary: '接收企业微信客服消息事件',
    description:
      '接收企业微信推送的客服消息事件，解密后获取token和cursor，然后拉取增量消息进行处理',
  })
  @ApiResponse({ status: 200, description: '返回success表示处理成功' })
  @Post('callback')
  async receiveMessageEvent(
    @Query('msg_signature') msgSignature: string,
    @Query('timestamp') timestamp: string,
    @Query('nonce') nonce: string,
    @Req() req: ExtendedRequest,
    @Res() res: Response,
  ) {
    try {
      this.logger.log('收到客服消息事件回调', {
        msgSignature,
        timestamp,
        nonce,
        contentType: req.headers['content-type'],
        bodyType: typeof req.body,
        bodyLength: (req.body as any)?.length || 0,
      });

      // 验证必要参数
      if (!msgSignature || !timestamp || !nonce) {
        this.logger.error('缺少必要的查询参数');
        return res.status(400).send('missing required parameters');
      }

      // 获取原始XML数据
      let xmlData: string;

      // 优先使用原始数据
      if (req.rawBody) {
        xmlData = req.rawBody;
      } else if (typeof req.body === 'string') {
        xmlData = req.body;
      } else if (Buffer.isBuffer(req.body)) {
        xmlData = req.body.toString('utf8');
      } else {
        this.logger.error('无法获取原始XML数据');
        return res.status(400).send('invalid request body');
      }

      if (!xmlData || xmlData.length === 0) {
        this.logger.error('XML数据为空');
        return res.status(400).send('empty xml data');
      }

      this.logger.log('解析到的XML数据长度:', xmlData.length);

      // 处理客服消息事件
      await this.wecomService.handleKfMessageEvent(
        msgSignature,
        timestamp,
        nonce,
        xmlData,
      );

      this.logger.log('客服消息事件处理成功');
      return res.send('success');
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error('处理客服消息事件失败:', {
        error: errorMessage,
        stack: errorStack,
        msgSignature,
        timestamp,
        nonce,
      });

      // 根据错误类型返回不同的状态码
      if (
        errorMessage.includes('签名验证失败') ||
        errorMessage.includes('缺少必要')
      ) {
        return res.status(400).send('verification failed');
      } else if (
        errorMessage.includes('解密失败') ||
        errorMessage.includes('XML解析失败')
      ) {
        return res.status(422).send('data processing failed');
      } else {
        return res.status(500).send('internal server error');
      }
    }
  }
}
