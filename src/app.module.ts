import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { LoggerModule } from './common/logger/logger.module';
import { UserModule } from './modules/user/user.module';
import { BindingModule } from './modules/binding/binding.module';
import { WecomModule } from './modules/wecom/wecom.module';
import { AiModule } from './modules/ai/ai.module';
import { NotionModule } from './modules/notion/notion.module';
import { FeishuModule } from './modules/feishu/feishu.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { UsageModule } from './modules/usage/usage.module';
import { BillingModule } from './modules/billing/billing.module';
import { QueueModule } from './modules/queue/queue.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    LoggerModule,
    PrismaModule,
    UserModule,
    BindingModule,
    WecomModule,
    AiModule,
    NotionModule,
    FeishuModule,
    SubscriptionModule,
    UsageModule,
    BillingModule,
    QueueModule,
    HealthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
