import { Module, forwardRef } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  MessageProcessor,
  AiAnalysisProcessor,
  DataSyncProcessor
} from './processors/message.processor';
import { QueueService } from './queue.service';
import { QueueController } from './queue.controller';
import { AiModule } from '../ai/ai.module';
import { NotionModule } from '../notion/notion.module';
import { FeishuModule } from '../feishu/feishu.module';
import { BindingModule } from '../binding/binding.module';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'RABBITMQ_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBITMQ_URL', 'amqp://localhost:5672')],
            queue: 'main_queue',
            queueOptions: {
              durable: true,
            },
            socketOptions: {
              heartbeatIntervalInSeconds: 60,
              reconnectTimeInSeconds: 5,
            },
          },
        }),
        inject: [ConfigService],
      },
    ]),
    AiModule,
    NotionModule,
    FeishuModule,
    BindingModule,
    forwardRef(() => import('../wecom/wecom.module').then(m => m.WecomModule)),
  ],
  controllers: [
    QueueController,
    MessageProcessor,
    AiAnalysisProcessor,
    DataSyncProcessor
  ],
  providers: [
    QueueService
  ],
  exports: [QueueService],
})
export class QueueModule {}
