import { Controller, Get, Res, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('serviceWorker.js')
  getServiceWorker(@Res() res: Response) {
    // 返回一个空的 service worker 文件，避免 404 错误
    res.setHeader('Content-Type', 'application/javascript');
    res.status(HttpStatus.OK).send('// Empty service worker');
  }

  @Get('favicon.ico')
  getFavicon(@Res() res: Response) {
    // 返回 204 No Content，避免 favicon 404 错误
    res.status(HttpStatus.NO_CONTENT).send();
  }
}
