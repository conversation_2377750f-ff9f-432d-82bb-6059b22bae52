import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RawBodyMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RawBodyMiddleware.name);

  use(req: Request, _res: Response, next: NextFunction): void {
    // 只对企业微信回调路径处理原始数据
    if (req.path === '/wecom/callback' && req.method === 'POST') {
      // 确保原始数据可用
      if (Buffer.isBuffer(req.body)) {
        (req as any).rawBody = req.body.toString('utf8');
        this.logger.log('Raw body from buffer:', {
          path: req.path,
          method: req.method,
          dataLength: (req as any).rawBody.length,
        });
      } else if (typeof req.body === 'string') {
        (req as any).rawBody = req.body;
        this.logger.log('Raw body from string:', {
          path: req.path,
          method: req.method,
          dataLength: req.body.length,
        });
      } else {
        this.logger.warn('Unexpected body type:', {
          path: req.path,
          method: req.method,
          bodyType: typeof req.body,
          body: req.body,
        });
      }
    }
    next();
  }
}
