# Notion 公开集成设置指南

## 概述

FlashBookkeeping 使用 Notion 的公开集成（Public Integration）功能来同步记账数据。本指南将帮助您设置 Notion 集成。

## 步骤 1: 创建 Notion 公开集成

1. **访问 Notion 集成页面**
   - 打开 [https://www.notion.so/my-integrations](https://www.notion.so/my-integrations)
   - 使用您的 Notion 账户登录

2. **创建新的集成**
   - 点击 "New integration" 按钮
   - 选择 "Public integration"

3. **配置集成信息**
   - **Name**: FlashBookkeeping（或您喜欢的名称）
   - **Logo**: 可选，上传应用图标
   - **Description**: 智能记账应用，自动同步记账数据到 Notion
   - **Website**: 您的应用网站（可选）

4. **设置权限**
   选择以下权限：
   - ✅ **Read content**: 读取数据库结构
   - ✅ **Insert content**: 插入新的记账记录
   - ✅ **Update content**: 更新记账记录（可选）
   - ❌ **Read user information**: 不需要
   - ❌ **Read comments**: 不需要

5. **配置重定向 URL**
   在 "Redirect URIs" 部分添加：
   ```
   http://localhost:3000/notion/callback
   ```
   
   如果是生产环境，使用：
   ```
   https://yourdomain.com/notion/callback
   ```

6. **提交审核**
   - 填写完所有信息后，点击 "Submit for review"
   - Notion 团队会审核您的集成申请
   - 审核通过后，您将收到邮件通知

## 步骤 2: 获取集成凭据

审核通过后：

1. **获取 Client ID**
   - 在集成详情页面找到 "OAuth Domain & URIs" 部分
   - 复制 "Client ID"

2. **获取 Client Secret**
   - 在同一页面找到 "Client Secret"
   - 点击 "Show" 并复制密钥

## 步骤 3: 配置应用

1. **更新环境变量**
   在 `.env` 文件中设置：
   ```bash
   NOTION_CLIENT_ID="your_actual_client_id"
   NOTION_CLIENT_SECRET="your_actual_client_secret"
   NOTION_REDIRECT_URI="http://localhost:3000/notion/callback"
   ```

2. **重启应用**
   ```bash
   pnpm run start:dev
   ```

## 步骤 4: 准备 Notion 数据库

1. **创建记账数据库**
   在您的 Notion 工作区中创建一个新的数据库，包含以下字段：

   | 字段名 | 类型 | 说明 |
   |--------|------|------|
   | 描述 | Title | 记账描述（必需） |
   | 金额 | Number | 消费金额 |
   | 分类 | Select | 消费类别 |
   | 日期 | Date | 消费日期 |
   | 置信度 | Number | AI识别置信度（可选） |

2. **设置分类选项**
   在 "分类" 字段中预设以下选项：
   - 餐饮
   - 交通
   - 购物
   - 娱乐
   - 住房
   - 医疗
   - 教育
   - 其他

## 步骤 5: 测试集成

1. **启动应用**
   ```bash
   pnpm run start:dev
   ```

2. **获取授权链接**
   访问：`http://localhost:3000/notion/auth-url?userId=test-user`

3. **完成授权流程**
   - 点击返回的授权链接
   - 在 Notion 中授权应用访问
   - 选择要同步的数据库
   - 完成绑定

## 常见问题

### Q: 集成审核需要多长时间？
A: 通常需要 1-3 个工作日，复杂的集成可能需要更长时间。

### Q: 审核被拒绝怎么办？
A: 根据 Notion 的反馈修改集成配置，确保：
- 应用描述清晰
- 权限请求合理
- 重定向 URL 正确

### Q: 如何更新集成配置？
A: 在 [https://www.notion.so/my-integrations](https://www.notion.so/my-integrations) 中编辑您的集成。

### Q: 数据库字段名称可以自定义吗？
A: 可以，应用会自动匹配包含关键词的字段：
- 金额：`金额`、`amount`、`价格`、`费用`
- 分类：`分类`、`category`、`类型`、`type`
- 日期：`日期`、`date`、`时间`、`time`

### Q: 支持多个数据库吗？
A: 目前每个用户只能绑定一个数据库，如需更换，重新授权即可。

## 安全注意事项

1. **保护密钥安全**
   - 不要在代码中硬编码 Client Secret
   - 使用环境变量存储敏感信息
   - 定期轮换密钥

2. **最小权限原则**
   - 只请求必要的权限
   - 定期审查权限使用情况

3. **数据隐私**
   - 确保用户了解数据同步范围
   - 提供数据删除选项
   - 遵守相关隐私法规

## 生产环境配置

部署到生产环境时：

1. **更新重定向 URL**
   ```
   https://yourdomain.com/notion/callback
   ```

2. **使用 HTTPS**
   确保所有通信都通过 HTTPS 进行

3. **监控和日志**
   - 监控 API 调用频率
   - 记录错误和异常
   - 设置告警机制

## 支持

如果在设置过程中遇到问题：

1. 查看 [Notion API 文档](https://developers.notion.com/)
2. 检查应用日志中的错误信息
3. 联系技术支持
