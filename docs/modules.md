# 1. 后端模块设计

为了实现高内聚、低耦合的架构，后端服务将按照业务领域划分为以下核心模块。

## 1.1. 核心业务模块

### 1.1.1. 用户模块 (`UserModule`)
- **核心职责**: 管理用户信息和认证。
- **对外接口**:
    - `createUser(externalUserId: string)`: 当新用户通过企业微信接入时，创建用户记录。
    - `findUser(externalUserId: string)`: 根据企业微信 ID 查询用户信息。

### 1.1.2. 微信模块 (`WeComModule`)
- **核心职责**: 处理与企业微信平台的所有交互，包括接收消息、发送消息和安全校验。
- **对外接口**:
    - `handleWebhook(payload: any)`: 接收并验证企业微信推送的 Webhook 事件。
    - `sendMessage(toUserId: string, message: any)`: 向指定用户发送消息（如记账结果、系统通知）。
    - `getMedia(mediaId: string)`: 下载用户发送的图片、语音等临时素材。

### 1.1.3. 账单模块 (`BillModule`)
- **核心职责**: 负责记账的核心业务逻辑，包括调用 LLM 进行识别和格式化。
- **对外接口**:
    - `processBill(userId: string, message: any)`: 接收原始消息，分发到消息队列，启动完整的记账流程。
    - `recognizeAndFormat(content: any)`: (内部逻辑) 调用大模型服务，提取关键信息并格式化为标准账单结构。

### 1.1.4. 订阅模块 (`SubscriptionModule`)
- **核心职责**: 管理用户的订阅状态、套餐和支付。
- **对外接口**:
    - `createSubscription(userId: string, plan: string)`: 创建支付订单，引导用户完成订阅。
    - `checkSubscription(userId: string)`: 检查用户的当前订阅状态和有效期。
    - `handlePaymentCallback(payload: any)`: 处理支付成功后的回调，更新用户订阅状态。

### 1.1.5. 用量统计模块 (`UsageStatsModule`)
- **核心职责**: 跟踪和管理免费用户的记账条目使用情况。
- **对外接口**:
    - `incrementUsage(userId: string)`: 记账成功后，增加用户的当月使用条目计数。
    - `checkUsageLimit(userId: string)`: 检查用户是否已达到免费额度上限。
    - `resetMonthlyUsage()`: (定时任务) 每月初重置所有免费用户的用量计数。

## 1.2. 第三方集成模块

### 1.2.1. Notion 模块 (`NotionModule`)
- **核心职责**: 封装与 Notion API 的所有交互，包括授权和数据写入。
- **对外接口**:
    - `getAuthUrl()`: 生成 Notion OAuth 授权链接。
    - `handleAuthCallback(code: string)`: 处理 OAuth 回调，获取 `access_token`。
    - `createPage(token: string, databaseId: string, data: any)`: 在指定的 Notion Database 中创建一条新记录。
    - `saveBinding(userId: string, token: string, databaseId: string)`: 将用户与 Notion 的绑定关系存入数据库。

### 1.2.2. 飞书模块 (`FeishuModule`)
- **核心职责**: 封装与飞书开放平台 API 的所有交互。
- **对外接口**:
    - `getAuthUrl()`: 生成飞书 OAuth 授权链接。
    - `handleAuthCallback(code: string)`: 处理 OAuth 回调，获取 `access_token`。
    - `addRecord(token: string, appToken: string, tableId: string, data: any)`: 在指定的多维表格中新增一行记录。
    - `saveBinding(userId: string, token: string, appToken: string, tableId: string)`: 将用户与飞书的绑定关系存入数据库。

### 1.2.3. 大模型模块 (`LlmModule`)
- **核心职责**: 封装对豆包等多模态大语言模型的 API 调用。
- **对外接口**:
    - `analyzeContent(content: string | Buffer)`: 向 LLM 发送文本或图片内容，返回结构化的 JSON 数据。

## 1.3. 共享与核心模块

### 1.3.1. 核心模块 (`CoreModule`)
- **职责**: 提供全局性的功能，如全局异常过滤器、日志记录器、配置管理等。此模块将被所有其他模块导入。

### 1.3.2. 共享模块 (`SharedModule`)
- **职责**: 提供可在多个业务模块之间复用的通用服务、工具函数或常量。例如，一个统一的日期处理服务。
