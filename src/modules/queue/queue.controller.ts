import { Controller, Get, Post, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { QueueService } from './queue.service';

@ApiTags('消息队列监控')
@Controller('queue')
export class QueueController {
  private readonly logger = new Logger(QueueController.name);

  constructor(private readonly queueService: QueueService) {}

  @ApiOperation({
    summary: '获取队列状态',
    description: '获取所有消息队列的状态统计信息',
  })
  @ApiResponse({ status: 200, description: '队列状态信息' })
  @Get('stats')
  getQueueStats() {
    try {
      const stats = this.queueService.getQueueStats();
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('获取队列状态失败:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @ApiOperation({
    summary: '清理失败任务',
    description: '清理24小时前的失败任务',
  })
  @ApiResponse({ status: 200, description: '清理完成' })
  @Post('clean-failed')
  cleanFailedJobs() {
    try {
      this.queueService.cleanFailedJobs();
      return {
        success: true,
        message: '失败任务清理完成',
      };
    } catch (error) {
      this.logger.error('清理失败任务出错:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @ApiOperation({
    summary: '队列健康检查',
    description: '检查消息队列系统的健康状态',
  })
  @ApiResponse({ status: 200, description: '健康状态' })
  @Get('health')
  checkHealth() {
    try {
      const stats = this.queueService.getQueueStats();

      // 检查是否有过多的失败任务
      const totalFailed = Object.values(stats).reduce(
        (sum: number, queueStats: any) => {
          return sum + (queueStats.failed || 0);
        },
        0,
      );

      const totalActive = Object.values(stats).reduce(
        (sum: number, queueStats: any) => {
          return sum + (queueStats.active || 0);
        },
        0,
      );

      const totalWaiting = Object.values(stats).reduce(
        (sum: number, queueStats: any) => {
          return sum + (queueStats.waiting || 0);
        },
        0,
      );

      const isHealthy = totalFailed < 100 && totalActive < 1000; // 简单的健康检查规则

      return {
        success: true,
        healthy: isHealthy,
        stats: {
          totalFailed,
          totalActive,
          totalWaiting,
        },
        queues: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('队列健康检查失败:', error);
      return {
        success: false,
        healthy: false,
        error: error.message,
      };
    }
  }
}
