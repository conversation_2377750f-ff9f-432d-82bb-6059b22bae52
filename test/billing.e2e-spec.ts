import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';

describe('BillingController (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    await app.init();
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await app.close();
  });

  beforeEach(async () => {
    // 清理测试数据
    await prisma.usageStat.deleteMany();
    await prisma.subscription.deleteMany();
    await prisma.binding.deleteMany();
    await prisma.user.deleteMany();
  });

  describe('/billing/binding-status/:userId (GET)', () => {
    it('should return binding status for user', async () => {
      // 创建测试用户
      const user = await prisma.user.create({
        data: {
          id: 'test-user-1',
          wecom_user_id: 'wecom-test-1',
          name: 'Test User',
          state: 'active',
          usageCount: 0,
        },
      });

      // 创建绑定
      await prisma.binding.create({
        data: {
          user_id: user.id,
          type: 'NOTION',
          access_token: 'encrypted-token',
          target_id: 'notion-db-id',
          target_name: 'Test Database',
          is_active: true,
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/billing/binding-status/${user.id}`)
        .expect(200);

      expect(response.body.hasNotion).toBe(true);
      expect(response.body.hasFeishu).toBe(false);
      expect(response.body.totalBindings).toBe(1);
    });

    it('should return empty binding status for new user', async () => {
      const user = await prisma.user.create({
        data: {
          id: 'test-user-2',
          wecom_user_id: 'wecom-test-2',
          name: 'Test User 2',
          state: 'active',
          usageCount: 0,
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/billing/binding-status/${user.id}`)
        .expect(200);

      expect(response.body.hasNotion).toBe(false);
      expect(response.body.hasFeishu).toBe(false);
      expect(response.body.totalBindings).toBe(0);
    });
  });

  describe('/billing/handle-command (POST)', () => {
    it('should handle binding command', async () => {
      const response = await request(app.getHttpServer())
        .post('/billing/handle-command')
        .send({
          userId: 'test-user',
          command: '绑定notion',
        })
        .expect(201);

      expect(response.body.response).toContain('Notion');
    });

    it('should handle help command', async () => {
      const response = await request(app.getHttpServer())
        .post('/billing/handle-command')
        .send({
          userId: 'test-user',
          command: '帮助',
        })
        .expect(201);

      expect(response.body.response).toContain('使用指南');
    });

    it('should return null for non-command text', async () => {
      const response = await request(app.getHttpServer())
        .post('/billing/handle-command')
        .send({
          userId: 'test-user',
          command: '午餐 25元',
        })
        .expect(201);

      expect(response.body.response).toBeNull();
    });
  });
});
