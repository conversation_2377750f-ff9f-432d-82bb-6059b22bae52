import { <PERSON>du<PERSON> } from '@nestjs/common';
import { BillingController } from './billing.controller';
import { BillingService } from './billing.service';
import { AiModule } from '../ai/ai.module';
import { NotionModule } from '../notion/notion.module';
import { FeishuModule } from '../feishu/feishu.module';
import { UsageModule } from '../usage/usage.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { WecomModule } from '../wecom/wecom.module';
import { UserModule } from '../user/user.module';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [
    AiModule,
    NotionModule,
    FeishuModule,
    UsageModule,
    SubscriptionModule,
    WecomModule,
    UserModule,
    PrismaModule,
  ],
  controllers: [BillingController],
  providers: [BillingService],
  exports: [BillingService],
})
export class BillingModule {}
