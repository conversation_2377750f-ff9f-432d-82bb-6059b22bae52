import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AiService, MessageType } from './ai.service';
import { of } from 'rxjs';

describe('AiService', () => {
  let service: AiService;
  let httpService: jest.Mocked<HttpService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiService,
        {
          provide: HttpService,
          useValue: {
            post: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AiService>(AiService);
    httpService = module.get(HttpService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('extractBillingInfo', () => {
    beforeEach(() => {
      configService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'DOUBAO_API_KEY':
            return 'test-api-key';
          case 'DOUBAO_API_ENDPOINT':
            return 'https://test-endpoint.com';
          default:
            return undefined;
        }
      });
    });

    it('should extract billing info from text successfully', async () => {
      // Arrange
      const mockResponse = {
        data: {
          choices: [
            {
              message: {
                content: JSON.stringify({
                  amount: 25,
                  category: '餐饮',
                  date: '2023-07-12',
                  description: '午餐 麦当劳',
                  confidence: 0.9,
                }),
              },
            },
          ],
        },
      };

      httpService.post.mockReturnValue(of(mockResponse) as any);

      const messageContent = {
        type: MessageType.TEXT,
        content: '午餐 25元 麦当劳',
      };

      // Act
      const result = await service.extractBillingInfo(messageContent);

      // Assert
      expect(result.amount).toBe(25);
      expect(result.category).toBe('餐饮');
      expect(result.description).toBe('午餐 麦当劳');
      expect(result.error).toBeUndefined();
    });

    it('should handle API configuration missing', async () => {
      // Arrange
      configService.get.mockReturnValue(undefined);

      const messageContent = {
        type: MessageType.TEXT,
        content: '午餐 25元',
      };

      // Act
      const result = await service.extractBillingInfo(messageContent);

      // Assert
      expect(result.error).toBe('AI 服务未正确配置');
    });

    it('should handle unsupported message type', async () => {
      // Arrange
      const messageContent = {
        type: MessageType.VOICE,
        content: '',
        url: 'voice-url',
      };

      // Act
      const result = await service.extractBillingInfo(messageContent);

      // Assert
      expect(result.error).toBe('语音处理功能暂未实现');
    });
  });

  describe('validateBillingInfo', () => {
    it('should validate correct billing info', async () => {
      const billingInfo = {
        amount: 25,
        category: '餐饮',
        date: '2023-07-12',
        description: '午餐',
        confidence: 0.9,
      };

      const result = await service.validateBillingInfo(billingInfo);
      expect(result).toBe(true);
    });

    it('should reject billing info with error', async () => {
      const billingInfo = {
        amount: 25,
        category: '餐饮',
        date: '2023-07-12',
        description: '午餐',
        error: 'Some error',
      };

      const result = await service.validateBillingInfo(billingInfo);
      expect(result).toBe(false);
    });

    it('should reject billing info with invalid amount', async () => {
      const billingInfo = {
        amount: 0,
        category: '餐饮',
        date: '2023-07-12',
        description: '午餐',
      };

      const result = await service.validateBillingInfo(billingInfo);
      expect(result).toBe(false);
    });

    it('should reject billing info with invalid category', async () => {
      const billingInfo = {
        amount: 25,
        category: '无效分类',
        date: '2023-07-12',
        description: '午餐',
      };

      const result = await service.validateBillingInfo(billingInfo);
      expect(result).toBe(false);
    });

    it('should reject billing info with invalid date format', async () => {
      const billingInfo = {
        amount: 25,
        category: '餐饮',
        date: '2023/07/12',
        description: '午餐',
      };

      const result = await service.validateBillingInfo(billingInfo);
      expect(result).toBe(false);
    });
  });
});
