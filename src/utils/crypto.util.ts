import {
  createCipheriv,
  createDecipheriv,
  randomBytes,
  scryptSync,
} from 'crypto';
import { Injectable } from '@nestjs/common';

// 全局加密工具函数
let cryptoKey: Buffer | null = null;

function getKey(): Buffer {
  if (!cryptoKey) {
    const encryptionKey = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-chars!!';
    cryptoKey = scryptSync(encryptionKey, 'salt', 32);
  }
  return cryptoKey;
}

/**
 * 加密数据
 */
export function encryptData(text: string): string {
  const algorithm = 'aes-256-cbc';
  const key = getKey();
  const iv = randomBytes(16);
  const cipher = createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}.${encrypted}`;
}

/**
 * 解密数据
 */
export function decryptData(encryptedText: string): string {
  const algorithm = 'aes-256-cbc';
  const key = getKey();
  const [ivHex, encryptedData] = encryptedText.split('.');
  if (!ivHex || !encryptedData) {
    throw new Error('Invalid encrypted text format');
  }
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = createDecipheriv(algorithm, key, iv);
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

@Injectable()
export class CryptoUtil {
  private readonly algorithm = 'aes-256-cbc';
  private readonly key: Buffer;

  constructor() {
    const encryptionKey = process.env.ENCRYPTION_KEY;
    if (!encryptionKey) {
      throw new Error('ENCRYPTION_KEY is not configured');
    }
    // 使用scrypt从密码派生密钥
    this.key = scryptSync(encryptionKey, 'salt', 32);
  }

  /**
   * 加密数据
   * @param text 要加密的文本
   * @returns 返回格式为: iv.encryptedData
   */
  encrypt(text: string): string {
    return encryptData(text);
  }

  /**
   * 解密数据
   * @param encryptedText 加密文本(格式为iv.encryptedData)
   * @returns 解密后的原始文本
   */
  decrypt(encryptedText: string): string {
    return decryptData(encryptedText);
  }
}
