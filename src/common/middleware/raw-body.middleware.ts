import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RawBodyMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    // 只对企业微信回调路径处理原始数据
    if (req.path === '/wecom/callback' && req.method === 'POST') {
      let data = '';
      
      req.setEncoding('utf8');
      
      req.on('data', (chunk) => {
        data += chunk;
      });
      
      req.on('end', () => {
        // 将原始数据存储到 req.body
        (req as any).rawBody = data;
        req.body = data;
        next();
      });
      
      req.on('error', (error) => {
        console.error('Raw body middleware error:', error);
        next(error);
      });
    } else {
      next();
    }
  }
}
