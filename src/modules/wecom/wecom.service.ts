import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { getSignature, decrypt } from '@wecom/crypto';
import { parseStringPromise } from 'xml2js';
import { firstValueFrom } from 'rxjs';
import { UserService } from '../user/user.service';
import { PrismaService } from '../../prisma/prisma.service';
import { QueueService, MessageJob } from '../queue/queue.service';
import { AxiosResponse } from 'axios';

interface KfMessageEvent {
  Token: string;
  OpenKfId: string;
}

interface KfMessage {
  msgid: string;
  open_kfid: string;
  external_userid: string;
  send_time: number;
  origin: number;
  msgtype: string;
  text?: {
    content: string;
  };
  image?: {
    media_id: string;
  };
  voice?: {
    media_id: string;
  };
  file?: {
    media_id: string;
  };
}

interface PullKfMsgResponse {
  errcode: number;
  errmsg: string;
  next_cursor: string;
  has_more: 1 | 0;
  msg_list: KfMessage[];
}

@Injectable()
export class WecomService {
  private readonly logger = new Logger(WecomService.name);
  private readonly token: string;
  private readonly encodingAESKey: string;
  private readonly corpId: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly prisma: PrismaService,
    private readonly httpService: HttpService,
    private readonly queueService: QueueService,
  ) {
    // 从环境变量中获取企业微信配置
    this.token = this.configService.get<string>('WECOM_TOKEN') || '';
    this.encodingAESKey =
      this.configService.get<string>('WECOM_ENCODING_AES_KEY') || '';
    this.corpId = this.configService.get<string>('WECOM_CORP_ID') || '';

    if (!this.token || !this.encodingAESKey || !this.corpId) {
      throw new Error('企业微信配置不完整，请检查环境变量');
    }

    this.logger.log('企业微信服务初始化完成');
  }

  /**
   * 验证URL
   */
  verifyUrl(
    msgSignature: string,
    timestamp: string,
    nonce: string,
    echostr: string,
  ): string {
    try {
      // 验证签名
      const signature = getSignature(this.token, timestamp, nonce, echostr);
      if (signature !== msgSignature) {
        throw new Error('签名验证失败');
      }

      // 解密echostr
      const { message } = decrypt(this.encodingAESKey, echostr);
      this.logger.log('URL验证成功');
      return message;
    } catch (error) {
      this.logger.error('URL验证失败:', error);
      throw error;
    }
  }

  /**
   * 处理客服消息事件
   */
  async handleKfMessageEvent(
    msgSignature: string,
    timestamp: string,
    nonce: string,
    xmlData: string,
  ): Promise<void> {
    try {
      this.logger.log('开始处理客服消息事件');

      // 1. 验证签名并解密XML数据
      const signature = getSignature(this.token, timestamp, nonce, xmlData);
      if (signature !== msgSignature) {
        throw new Error('消息签名验证失败');
      }

      const { message } = decrypt(this.encodingAESKey, xmlData);
      this.logger.log('消息解密成功');

      // 2. 解析XML获取事件信息
      const parsedXml = (await parseStringPromise(message, {
        explicitArray: false,
        tagNameProcessors: [(name) => name],
      })) as {
        xml: {
          ToUserName: string;
          CreateTime: string;
          MsgType: string;
          Event: string;
          Token: string;
          OpenKfId: string;
        };
      };

      const eventData = parsedXml.xml;
      this.logger.log('解析事件数据:', eventData);

      // 3. 检查是否是客服消息事件
      if (eventData.Event === 'kf_msg_or_event') {
        const kfEvent: KfMessageEvent = {
          Token: eventData.Token,
          OpenKfId: eventData.OpenKfId,
        };

        this.logger.log('收到客服消息事件:', kfEvent);

        // 4. 使用Token和Cursor拉取增量消息
        await this.pullKfMessages(kfEvent);
      } else {
        this.logger.log('非客服消息事件，忽略处理');
      }
    } catch (error) {
      this.logger.error('处理客服消息事件失败:', error);
      throw error;
    }
  }

  /**
   * 拉取客服增量消息
   */
  private async pullKfMessages(event: KfMessageEvent): Promise<void> {
    try {
      this.logger.log('开始拉取客服增量消息');

      const accessToken = await this.getAccessToken();
      const apiUrl = `https://qyapi.weixin.qq.com/cgi-bin/kf/sync_msg?access_token=${accessToken}`;

      // 从数据库中读取cursor
      const currentCursor = await this.getCursor(event.OpenKfId);

      const requestData = {
        cursor: currentCursor,
        token: event.Token,
        limit: 1000, // 每次最多拉取1000条消息
        voice_format: 0,
        open_kfid: event.OpenKfId,
      };

      this.logger.log(`使用cursor: ${currentCursor} 拉取消息`);

      const response = await firstValueFrom<AxiosResponse<PullKfMsgResponse>>(
        this.httpService.post(apiUrl, requestData),
      );

      if (response.data.errcode !== 0) {
        throw new Error(`拉取消息失败: ${response.data.errmsg}`);
      }

      const { msg_list, next_cursor, has_more } = response.data;
      this.logger.log(
        `成功拉取到 ${msg_list?.length || 0} 条消息，next_cursor: ${next_cursor}`,
      );

      // 处理每条消息
      if (msg_list && msg_list.length > 0) {
        for (const message of msg_list) {
          await this.processKfMessage(message);
        }
      }

      // 更新cursor到数据库
      if (next_cursor) {
        await this.updateCursor(event.OpenKfId, next_cursor);
        this.logger.log(`已更新cursor: ${next_cursor}`);
      }

      // 如果还有更多消息，继续拉取
      if (has_more) {
        // 创建新的event对象，避免无限递归
        const nextEvent = { ...event };
        await this.pullKfMessages(nextEvent);
      }
    } catch (error) {
      this.logger.error('拉取客服消息失败:', error);
      throw error;
    }
  }

  /**
   * 处理单条客服消息
   */
  private async processKfMessage(message: KfMessage): Promise<void> {
    try {
      this.logger.log('处理客服消息:', message);

      // 只处理用户发送的消息（origin: 3表示用户发送）
      if (message.origin !== 3) {
        this.logger.log('非用户消息，跳过处理');
        return;
      }

      const userId = message.external_userid;
      const kfId = message.open_kfid;

      // 确保用户存在
      await this.ensureUserExists(userId);

      // 根据消息类型处理
      let messageContent = '';
      const messageType = message.msgtype;

      switch (messageType) {
        case 'text':
          messageContent = message.text?.content || '';
          break;
        case 'image':
          messageContent = `[图片消息: ${message.image?.media_id}]`;
          // TODO: 下载并处理图片
          break;
        case 'voice':
          messageContent = `[语音消息: ${message.voice?.media_id}]`;
          // TODO: 下载并处理语音
          break;
        case 'file':
          messageContent = `[文件消息: ${message.file?.media_id}]`;
          // TODO: 下载并处理文件
          break;
        default:
          this.logger.log(`不支持的消息类型: ${messageType}`);
          return;
      }

      this.logger.log('消息内容:', { userId, messageType, messageContent });

      // 发送处理中的提示消息
      await this.sendKfMessage(kfId, userId, '收到您的消息，正在智能分析中...');

      // 将消息发送到消息队列进行处理
      const messageJob: MessageJob = {
        userId,
        kfId,
        messageId: message.msgid,
        messageType,
        content: messageContent,
        mediaId:
          message.image?.media_id ||
          message.voice?.media_id ||
          message.file?.media_id,
        timestamp: message.send_time,
      };

      await this.queueService.addMessageProcessingJob(messageJob);
      this.logger.log(`消息已加入处理队列: ${message.msgid}`);
    } catch (error) {
      this.logger.error('处理客服消息失败:', error);
    }
  }

  /**
   * 发送客服消息
   */
  async sendKfMessage(
    kfId: string,
    userId: string,
    content: string,
  ): Promise<void> {
    try {
      const accessToken = await this.getAccessToken();
      const apiUrl = `https://qyapi.weixin.qq.com/cgi-bin/kf/send_msg?access_token=${accessToken}`;

      const messageData = {
        touser: userId,
        open_kfid: kfId,
        msgtype: 'text',
        text: {
          content,
        },
      };

      const response = await firstValueFrom<
        AxiosResponse<{ errcode: number; errmsg: string }>
      >(this.httpService.post(apiUrl, messageData));

      if (response.data.errcode !== 0) {
        throw new Error(`发送消息失败: ${response.data.errmsg}`);
      }

      this.logger.log('客服消息发送成功');
    } catch (error) {
      this.logger.error('发送客服消息失败:', error);
      throw error;
    }
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<string> {
    try {
      const corpId = this.configService.get<string>('WECOM_CORP_ID');
      const secret = this.configService.get<string>('WECOM_SECRET');

      const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpId}&corpsecret=${secret}`;

      const response = await firstValueFrom<
        AxiosResponse<{ errcode: number; errmsg: string; access_token: string }>
      >(this.httpService.get(url));

      if (response.data.errcode !== 0) {
        throw new Error(`获取访问令牌失败: ${response.data.errmsg}`);
      }

      return response.data.access_token;
    } catch (error) {
      this.logger.error('获取访问令牌失败:', error);
      throw error;
    }
  }

  /**
   * 确保用户存在
   */
  private async ensureUserExists(userId: string): Promise<void> {
    try {
      const existingUser = await this.userService.findOne(userId);
      if (!existingUser) {
        await this.userService.create({
          wecomUserId: userId,
          name: `用户_${userId.substring(0, 8)}`,
        });
        this.logger.log(`创建新用户: ${userId}`);
      }
    } catch (error) {
      this.logger.error('确保用户存在失败:', error);
    }
  }

  /**
   * 从数据库获取cursor
   */
  private async getCursor(openKfId: string): Promise<string> {
    try {
      const cursorRecord = await this.prisma.wecomSyncCursor.findUnique({
        where: { id: openKfId },
      });

      if (cursorRecord) {
        this.logger.log(`从数据库获取到cursor: ${cursorRecord.cursor}`);
        return cursorRecord.cursor;
      }

      // 如果没有找到cursor记录，返回空字符串（首次拉取）
      this.logger.log(`未找到cursor记录，使用空cursor进行首次拉取`);
      return '';
    } catch (error) {
      this.logger.error('获取cursor失败:', error);
      return ''; // 出错时返回空cursor
    }
  }

  /**
   * 更新cursor到数据库
   */
  private async updateCursor(openKfId: string, cursor: string): Promise<void> {
    try {
      await this.prisma.wecomSyncCursor.upsert({
        where: { id: openKfId },
        update: {
          cursor,
          updated_at: new Date(),
        },
        create: {
          id: openKfId,
          cursor,
          updated_at: new Date(),
        },
      });

      this.logger.log(`成功更新cursor到数据库: ${cursor}`);
    } catch (error) {
      this.logger.error('更新cursor失败:', error);
      throw error;
    }
  }
}
