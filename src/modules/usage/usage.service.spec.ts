import { Test, TestingModule } from '@nestjs/testing';
import { UsageService } from './usage.service';
import { PrismaService } from '../../prisma/prisma.service';

describe('UsageService', () => {
  let service: UsageService;
  let prismaService: jest.Mocked<PrismaService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsageService,
        {
          provide: PrismaService,
          useValue: {
            usageStat: {
              upsert: jest.fn(),
              findUnique: jest.fn(),
              findMany: jest.fn(),
              deleteMany: jest.fn(),
              count: jest.fn(),
              aggregate: jest.fn(),
            },
            user: {
              findUnique: jest.fn(),
              count: jest.fn(),
            },
            subscription: {
              count: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<UsageService>(UsageService);
    prismaService = module.get(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('incrementUsage', () => {
    it('should increment usage count', async () => {
      // Arrange
      const userId = 'user1';
      prismaService.usageStat.upsert.mockResolvedValue({
        id: 'stat1',
        user_id: userId,
        year_month: 202307,
        count: 1,
        updated_at: new Date(),
      });

      // Act
      await service.incrementUsage(userId);

      // Assert
      expect(prismaService.usageStat.upsert).toHaveBeenCalledWith({
        where: {
          user_id_year_month: {
            user_id: userId,
            year_month: expect.any(Number),
          },
        },
        update: {
          count: { increment: 1 },
        },
        create: {
          user_id: userId,
          year_month: expect.any(Number),
          count: 1,
        },
      });
    });
  });

  describe('getCurrentUsage', () => {
    it('should return current usage stats', async () => {
      // Arrange
      const userId = 'user1';
      const mockUsageStat = {
        id: 'stat1',
        user_id: userId,
        year_month: 202307,
        count: 15,
        updated_at: new Date(),
      };

      const mockUser = {
        id: userId,
        subscriptions: [
          {
            limit: 1000,
            status: 'active',
            end_date: new Date(Date.now() + 86400000), // tomorrow
          },
        ],
      };

      prismaService.usageStat.findUnique.mockResolvedValue(mockUsageStat);
      prismaService.user.findUnique.mockResolvedValue(mockUser as any);

      // Act
      const result = await service.getCurrentUsage(userId);

      // Assert
      expect(result.count).toBe(15);
      expect(result.limit).toBe(1000);
      expect(result.remaining).toBe(985);
      expect(result.percentage).toBe(1.5);
    });

    it('should handle user with no usage stats', async () => {
      // Arrange
      const userId = 'user1';
      prismaService.usageStat.findUnique.mockResolvedValue(null);
      prismaService.user.findUnique.mockResolvedValue({
        id: userId,
        subscriptions: [],
      } as any);

      // Act
      const result = await service.getCurrentUsage(userId);

      // Assert
      expect(result.count).toBe(0);
      expect(result.limit).toBe(30); // default free limit
      expect(result.remaining).toBe(30);
    });
  });

  describe('isOverLimit', () => {
    it('should return true when over limit', async () => {
      // Arrange
      const userId = 'user1';
      jest.spyOn(service, 'getCurrentUsage').mockResolvedValue({
        userId,
        yearMonth: 202307,
        count: 35,
        limit: 30,
        remaining: 0,
        percentage: 116.67,
      });

      // Act
      const result = await service.isOverLimit(userId);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false when under limit', async () => {
      // Arrange
      const userId = 'user1';
      jest.spyOn(service, 'getCurrentUsage').mockResolvedValue({
        userId,
        yearMonth: 202307,
        count: 15,
        limit: 30,
        remaining: 15,
        percentage: 50,
      });

      // Act
      const result = await service.isOverLimit(userId);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('getSystemStats', () => {
    it('should return system statistics', async () => {
      // Arrange
      prismaService.user.count.mockResolvedValue(100);
      prismaService.usageStat.count.mockResolvedValue(50);
      prismaService.usageStat.aggregate.mockResolvedValue({
        _sum: { count: 1500 },
      } as any);
      prismaService.subscription.count.mockResolvedValue(25);

      // Act
      const result = await service.getSystemStats();

      // Assert
      expect(result.totalUsers).toBe(100);
      expect(result.activeUsers).toBe(50);
      expect(result.totalUsage).toBe(1500);
      expect(result.activeSubscriptions).toBe(25);
    });
  });
});
