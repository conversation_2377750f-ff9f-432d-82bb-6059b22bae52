import { Body, Controller, Post } from '@nestjs/common';
import { AiService, BillingInfo } from './ai.service';
import { ExtractBillingDto } from './dto/extract-billing.dto';

@Controller('ai')
export class AiController {
  constructor(private readonly aiService: AiService) {}

  @Post('extract-billing')
  async extractBilling(@Body() dto: ExtractBillingDto): Promise<BillingInfo> {
    return await this.aiService.extractBillingInfo({
      type: dto.type,
      content: dto.content,
      url: dto.url,
      mimeType: dto.mimeType,
    });
  }

  @Post('validate-billing')
  async validateBilling(@Body() billingInfo: BillingInfo): Promise<{ valid: boolean }> {
    const valid = await this.aiService.validateBillingInfo(billingInfo);
    return { valid };
  }
}
