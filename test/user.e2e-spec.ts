import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from './../src/app.module';

import { PrismaService } from '../src/prisma/prisma.service';

describe('UserController (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = app.get(PrismaService);
    await app.init();
  });

  beforeEach(async () => {
    await prisma.user.deleteMany({});
  });

  afterAll(async () => {
    await app.close();
  });

  it('/users (POST)', async () => {
    const createUserDto = { wecomUserId: 'test-user', name: 'Test User' };
    return request(app.getHttpServer())
      .post('/users')
      .send(createUserDto)
      .expect(201)
      .then((res) => {
        expect(res.body).toEqual({
          id: 'test-user',
          name: 'Test User',
          created_at: expect.any(String),
          updated_at: expect.any(String),
        });
      });
  });

  it('/users (GET)', async () => {
    await prisma.user.create({
      data: { id: 'test-user', name: 'Test User' },
    });

    return request(app.getHttpServer())
      .get('/users')
      .expect(200)
      .then((res) => {
        expect(res.body).toEqual([
          {
            id: 'test-user',
            name: 'Test User',
            created_at: expect.any(String),
            updated_at: expect.any(String),
          },
        ]);
      });
  });

  it('/users/:id (GET)', async () => {
    await prisma.user.create({
      data: { id: 'test-user', name: 'Test User' },
    });

    return request(app.getHttpServer())
      .get('/users/test-user')
      .expect(200)
      .then((res) => {
        expect(res.body).toEqual({
          id: 'test-user',
          name: 'Test User',
          created_at: expect.any(String),
          updated_at: expect.any(String),
        });
      });
  });

  it('/users/:id (PATCH)', async () => {
    await prisma.user.create({
      data: { id: 'test-user', name: 'Test User' },
    });

    const updateUserDto = { name: 'Updated User' };
    return request(app.getHttpServer())
      .patch('/users/test-user')
      .send(updateUserDto)
      .expect(200)
      .then((res) => {
        expect(res.body).toEqual({
          id: 'test-user',
          name: 'Updated User',
          created_at: expect.any(String),
          updated_at: expect.any(String),
        });
      });
  });

  it('/users/:id (DELETE)', async () => {
    await prisma.user.create({
      data: { id: 'test-user', name: 'Test User' },
    });

    return request(app.getHttpServer())
      .delete('/users/test-user')
      .expect(200);
  });
});
