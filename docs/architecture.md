# 1. 系统架构设计

本文档描述了“闪电记账”项目的整体技术架构、数据流和关键技术选型。

## 1.1. 整体架构图

系统采用分层、模块化的架构，确保高内聚、低耦合，易于扩展和维护。核心数据流如下：

1.  **用户输入**：用户通过企业微信客户端向客服机器人发送记账信息。
2.  **消息接收**：企业微信通过 Webhook 将用户消息推送到我们的后端服务。
3.  **网关与服务**：Nginx 作为反向代理接收请求，并转发给后端的 NestJS 应用。
4.  **异步处理**：NestJS 应用接收到消息后，立即将其放入消息队列（如 RabbitMQ），并快速响应企业微信，避免超时。
5.  **智能识别**：一个独立的 Worker 服务从队列中获取任务，调用多模态大模型（豆包LLM）API，对消息内容（文本、图片等）进行识别和结构化。
6.  **数据持久化**：Worker 服务将用户信息、绑定关系、用量等数据与 PostgreSQL 数据库进行交互。
7.  **目标写入**：Worker 服务根据用户的绑定信息，调用 Notion 或飞书的开放 API，将格式化后的账目数据写入指定的数据库或多维表格。
8.  **结果反馈**：完成后，通过企业微信的机器人回调接口，向用户发送成功或失败的通知。

```mermaid
sequenceDiagram
    participant User as 用户 (企业微信)
    participant WeCom as 企业微信平台
    participant Nginx as 反向代理
    participant NestApp as NestJS 应用
    participant MsgQueue as 消息队列
    participant Worker as 后端 Worker
    participant LLM as 豆包LLM
    participant DB as PostgreSQL 数据库
    participant Notion as Notion API
    participant Feishu as 飞书 API

    User->>WeCom: 发送记账消息 (文本/图片)
    WeCom->>Nginx: 调用 Webhook
    Nginx->>NestApp: 转发请求
    NestApp->>MsgQueue: 将消息放入队列
    NestApp-->>WeCom: 立即响应 (ACK)
    
    Worker->>MsgQueue: 获取记账任务
    Worker->>LLM: 发送内容进行识别
    LLM-->>Worker: 返回结构化数据
    
    Worker->>DB: 查询用户绑定信息 (Token等)
    DB-->>Worker: 返回绑定信息
    
    alt 目标是 Notion
        Worker->>Notion: 写入格式化账目
        Notion-->>Worker: 返回写入结果
    else 目标是 飞书
        Worker->>Feishu: 写入格式化账目
        Feishu-->>Worker: 返回写入结果
    end

    Worker->>DB: 更新用量统计
    Worker->>WeCom: 通过API发送结果通知
    WeCom->>User: 显示记账结果
```

## 1.2. 关键技术选型

| 技术栈 | 选型 | 主要原因 |
| :--- | :--- | :--- |
| **后端框架** | **NestJS** | - **模块化架构**：与项目模块化设计的思想高度契合，便于团队协作和长期维护。<br>- **TypeScript 支持**：提供强大的类型系统和面向对象编程范式，提升代码质量和开发效率。<br>- **生态系统**：拥有丰富的生态和插件（如 `@nestjs/swagger`, `@nestjs/passport`），可快速集成常用功能。 |
| **容器化** | **Docker** | - **环境一致性**：确保开发、测试、生产环境的完全一致，避免“在我电脑上能跑”的问题。<br>- **简化部署**：通过 `docker-compose` 实现一键部署整个应用栈（应用、数据库、Nginx等），极大降低部署复杂性。<br>- **隔离与安全**：为每个服务提供隔离的运行环境，提升系统稳定性。 |
| **Web 服务器** | **Nginx** | - **高性能**：作为高性能的反向代理和负载均衡器，能轻松处理大量并发连接。<br>- **功能强大**：可用于 SSL 证书管理与自动续签、请求限流、静态资源服务等。 |
| **数据库** | **PostgreSQL** | - **功能强大且稳定**：作为一款成熟的开源关系型数据库，支持复杂的查询、事务和 JSON 数据类型，性能稳定可靠。<br>- **可扩展性**：支持丰富的扩展，能满足未来可能出现的复杂数据处理需求。<br>- **社区活跃**：拥有庞大且活跃的社区，文档和解决方案丰富。 |
| **多模态模型** | **豆包LLM** | - **多模态能力**：能够处理文本、图片等多种输入格式，完全符合项目从不同类型消息中提取账目信息的核心需求。<br>- **成本与性能**：在提供强大功能的同时，具有较好的成本效益和响应速度，适合初创项目。 |
| **消息队列** | **RabbitMQ (待定)** | - **服务解耦**：将消息接收和处理流程解耦，提升系统的响应速度和鲁棒性。<br>- **异步处理**：允许将耗时的 LLM 调用和第三方 API 调用作为后台任务执行，避免阻塞主服务。<br>- **削峰填谷**：在高并发场景下，可以平滑处理突发流量，保护后端服务。 |
