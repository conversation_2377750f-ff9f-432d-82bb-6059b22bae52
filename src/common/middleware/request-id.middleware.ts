import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const requestId = (req.headers['x-request-id'] as string) || uuidv4();

    // 设置请求ID到请求对象
    (req as any).requestId = requestId;

    // 设置响应头
    res.setHeader('X-Request-ID', requestId);

    next();
  }
}
