import { Injectable, Logger, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

export interface MessageJob {
  userId: string;
  kfId: string;
  messageId: string;
  messageType: string;
  content: string;
  mediaId?: string;
  timestamp: number;
}

export interface AiAnalysisJob {
  userId: string;
  kfId: string;
  messageId: string;
  messageType: string;
  content: string;
  mediaUrl?: string;
}

export interface DataSyncJob {
  userId: string;
  billingInfo: {
    amount: number;
    category: string;
    date: string;
    description: string;
  };
  platforms: string[];
}

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);
  private queueStats = {
    messageProcessing: {
      waiting: 0,
      active: 0,
      completed: 0,
      failed: 0,
      delayed: 0,
    },
    aiAnalysis: { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 },
    dataSync: { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 },
  };

  constructor(
    @Inject('RABBITMQ_SERVICE') private readonly rabbitClient: ClientProxy,
  ) {}

  /**
   * 添加消息处理任务
   */
  async addMessageProcessingJob(data: MessageJob): Promise<void> {
    try {
      await firstValueFrom(
        this.rabbitClient.emit('process-message', {
          pattern: 'process-message',
          data,
          priority: 10,
          timestamp: Date.now(),
        }),
      );
      this.queueStats.messageProcessing.waiting++;
      this.logger.log(`消息处理任务已添加: ${data.messageId}`);
    } catch (error) {
      this.logger.error('添加消息处理任务失败:', error);
      this.queueStats.messageProcessing.failed++;
      this.logger.warn('RabbitMQ不可用，将直接处理消息');
      // TODO: 在RabbitMQ不可用时，可以直接调用处理逻辑
      // 这里暂时只记录警告，不抛出错误
    }
  }

  /**
   * 添加AI分析任务
   */
  async addAiAnalysisJob(data: AiAnalysisJob): Promise<void> {
    try {
      // 延迟1秒执行，给消息处理时间
      await new Promise((resolve) => setTimeout(resolve, 1000));

      await firstValueFrom(
        this.rabbitClient.emit('analyze-message', {
          pattern: 'analyze-message',
          data,
          priority: 5,
          timestamp: Date.now(),
        }),
      );

      this.queueStats.aiAnalysis.waiting++;
      this.logger.log(`AI分析任务已添加: ${data.messageId}`);
    } catch (error) {
      this.logger.error('添加AI分析任务失败:', error);
      this.queueStats.aiAnalysis.failed++;
      throw error;
    }
  }

  /**
   * 添加数据同步任务
   */
  async addDataSyncJob(data: DataSyncJob): Promise<void> {
    try {
      // 延迟2秒执行，确保AI分析完成
      await new Promise((resolve) => setTimeout(resolve, 2000));

      await firstValueFrom(
        this.rabbitClient.emit('sync-data', {
          pattern: 'sync-data',
          data,
          priority: 3,
          timestamp: Date.now(),
        }),
      );

      this.queueStats.dataSync.waiting++;
      this.logger.log(`数据同步任务已添加: ${data.userId}`);
    } catch (error) {
      this.logger.error('添加数据同步任务失败:', error);
      this.queueStats.dataSync.failed++;
      throw error;
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStats() {
    try {
      // RabbitMQ不像Redis Bull那样有内置的统计功能
      // 我们使用内存中的统计数据
      return {
        messageProcessing: this.queueStats.messageProcessing,
        aiAnalysis: this.queueStats.aiAnalysis,
        dataSync: this.queueStats.dataSync,
        rabbitMqConnected: true,
      };
    } catch (error) {
      this.logger.error('获取队列状态失败:', error);
      // 返回默认状态而不是抛出错误
      return {
        messageProcessing: {
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0,
        },
        aiAnalysis: {
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0,
        },
        dataSync: {
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0,
        },
        error: 'RabbitMQ连接失败，队列功能不可用',
        rabbitMqConnected: false,
      };
    }
  }

  /**
   * 清理失败的任务
   */
  cleanFailedJobs(): void {
    try {
      // RabbitMQ的失败任务清理需要通过管理API或者自定义逻辑
      // 这里我们重置内存中的失败计数
      this.queueStats.messageProcessing.failed = 0;
      this.queueStats.aiAnalysis.failed = 0;
      this.queueStats.dataSync.failed = 0;

      this.logger.log('失败任务统计已重置');
    } catch (error) {
      this.logger.error('清理失败任务出错:', error);
    }
  }

  /**
   * 更新队列统计
   */
  updateQueueStats(
    queueName: string,
    operation: 'completed' | 'failed' | 'active',
  ) {
    if (queueName === 'message-processing') {
      if (operation === 'completed') {
        this.queueStats.messageProcessing.waiting = Math.max(
          0,
          this.queueStats.messageProcessing.waiting - 1,
        );
        this.queueStats.messageProcessing.completed++;
      } else if (operation === 'failed') {
        this.queueStats.messageProcessing.waiting = Math.max(
          0,
          this.queueStats.messageProcessing.waiting - 1,
        );
        this.queueStats.messageProcessing.failed++;
      } else if (operation === 'active') {
        this.queueStats.messageProcessing.waiting = Math.max(
          0,
          this.queueStats.messageProcessing.waiting - 1,
        );
        this.queueStats.messageProcessing.active++;
      }
    } else if (queueName === 'ai-analysis') {
      if (operation === 'completed') {
        this.queueStats.aiAnalysis.waiting = Math.max(
          0,
          this.queueStats.aiAnalysis.waiting - 1,
        );
        this.queueStats.aiAnalysis.completed++;
      } else if (operation === 'failed') {
        this.queueStats.aiAnalysis.waiting = Math.max(
          0,
          this.queueStats.aiAnalysis.waiting - 1,
        );
        this.queueStats.aiAnalysis.failed++;
      } else if (operation === 'active') {
        this.queueStats.aiAnalysis.waiting = Math.max(
          0,
          this.queueStats.aiAnalysis.waiting - 1,
        );
        this.queueStats.aiAnalysis.active++;
      }
    } else if (queueName === 'data-sync') {
      if (operation === 'completed') {
        this.queueStats.dataSync.waiting = Math.max(
          0,
          this.queueStats.dataSync.waiting - 1,
        );
        this.queueStats.dataSync.completed++;
      } else if (operation === 'failed') {
        this.queueStats.dataSync.waiting = Math.max(
          0,
          this.queueStats.dataSync.waiting - 1,
        );
        this.queueStats.dataSync.failed++;
      } else if (operation === 'active') {
        this.queueStats.dataSync.waiting = Math.max(
          0,
          this.queueStats.dataSync.waiting - 1,
        );
        this.queueStats.dataSync.active++;
      }
    }
  }
}
