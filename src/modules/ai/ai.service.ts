import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

// 定义豆包 API 返回的数据结构
interface DoubaoResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

// 定义账单信息的接口
export interface BillingInfo {
  amount: number;
  category: string;
  date: string;
  description: string;
  confidence?: number; // 置信度
  error?: string;
}

// 定义支持的消息类型
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  FILE = 'file',
}

// 定义消息内容接口
export interface MessageContent {
  type: MessageType;
  content: string;
  url?: string; // 图片、语音、文件的URL
  mimeType?: string; // 文件类型
}

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 从队列任务中提取账单信息
   */
  async extractBillingInfoFromQueue(
    messageType: string,
    content: string,
    mediaUrl?: string,
  ): Promise<BillingInfo> {
    try {
      this.logger.log('开始AI分析队列任务');

      // 构建消息内容
      const messageContent: MessageContent = {
        type: this.getMessageType(messageType),
        content,
        url: mediaUrl,
      };

      return await this.extractBillingInfo(messageContent);
    } catch (error) {
      this.logger.error('AI分析队列任务失败:', error);
      throw error;
    }
  }

  /**
   * 将字符串消息类型转换为枚举
   */
  private getMessageType(messageType: string): MessageType {
    switch (messageType) {
      case 'text':
        return MessageType.TEXT;
      case 'image':
        return MessageType.IMAGE;
      case 'voice':
        return MessageType.VOICE;
      case 'file':
        return MessageType.FILE;
      default:
        return MessageType.TEXT;
    }
  }

  /**
   * 从多模态内容中提取账单信息
   * @param content 消息内容
   * @returns 提取的账单信息
   */
  async extractBillingInfo(content: MessageContent): Promise<BillingInfo> {
    // 临时使用模拟数据，直到豆包 API 配置正确
    this.logger.log('使用模拟 AI 服务处理账单信息提取');

    // 简单的文本解析逻辑
    let text = '';
    if (content.type === 'text' && 'content' in content) {
      text = content.content;
    }

    // 提取金额
    const amountMatch = text.match(/(\d+(?:\.\d+)?)元?/);
    const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;

    // 简单的类别判断
    let category = '其他';
    if (
      text.includes('咖啡') ||
      text.includes('星巴克') ||
      text.includes('餐') ||
      text.includes('吃')
    ) {
      category = '餐饮';
    } else if (
      text.includes('打车') ||
      text.includes('地铁') ||
      text.includes('公交')
    ) {
      category = '交通';
    } else if (text.includes('买') || text.includes('购')) {
      category = '购物';
    }

    // 获取今天的日期
    const today = new Date().toISOString().split('T')[0];

    return {
      amount,
      category,
      date: today,
      description: text || '未知消费',
      confidence: 0.8,
    };

    try {
      // 根据内容类型选择处理方式
      switch (content.type) {
        case MessageType.TEXT:
          return await this.processTextContent(content.content);
        case MessageType.IMAGE:
          return await this.processImageContent(content.url!);
        case MessageType.VOICE:
          return await this.processVoiceContent(content.url!);
        case MessageType.FILE:
          return await this.processFileContent(content.url!, content.mimeType);
        default:
          return this.createErrorResponse('不支持的消息类型');
      }
    } catch (error) {
      this.logger.error('提取账单信息失败:', error);
      return this.createErrorResponse('处理消息时发生错误');
    }
  }

  /**
   * 处理文本内容
   */
  private async processTextContent(text: string): Promise<BillingInfo> {
    const prompt = this.buildPrompt('text');
    return await this.callDoubaoAPI(prompt, text);
  }

  /**
   * 处理图片内容
   */
  private async processImageContent(imageUrl: string): Promise<BillingInfo> {
    const prompt = this.buildPrompt('image');
    return await this.callDoubaoAPI(prompt, '', imageUrl);
  }

  /**
   * 处理语音内容（需要先转文字）
   */
  private async processVoiceContent(voiceUrl: string): Promise<BillingInfo> {
    // TODO: 实现语音转文字功能
    this.logger.warn('语音处理功能暂未实现');
    return this.createErrorResponse('语音处理功能暂未实现');
  }

  /**
   * 处理文件内容
   */
  private async processFileContent(
    fileUrl: string,
    mimeType?: string,
  ): Promise<BillingInfo> {
    // TODO: 根据文件类型处理（PDF、Excel等）
    this.logger.warn('文件处理功能暂未实现');
    return this.createErrorResponse('文件处理功能暂未实现');
  }

  /**
   * 构建提示词
   */
  private buildPrompt(contentType: string): string {
    const basePrompt = `
你是一个专业的智能记账助手。请从${contentType === 'image' ? '图片' : '文本'}中提取关键的记账信息。

要求：
1. 必须返回有效的JSON格式
2. 如果信息不完整，请根据常识进行合理推断
3. 如果完全无法识别，请在error字段中说明原因
4. 金额必须是数字类型，不要包含货币符号
5. 日期格式必须是YYYY-MM-DD，如果没有日期信息，使用今天的日期

返回格式：
{
  "amount": 数字,
  "category": "餐饮|交通|购物|娱乐|住房|医疗|教育|其他",
  "date": "YYYY-MM-DD",
  "description": "详细描述",
  "confidence": 0.0-1.0,
  "error": "错误信息(可选)"
}

常见类别说明：
- 餐饮: 吃饭、喝咖啡、外卖等
- 交通: 打车、地铁、公交、加油等
- 购物: 买衣服、日用品、电子产品等
- 娱乐: 电影、游戏、旅游等
- 住房: 房租、水电费、物业费等
- 医疗: 看病、买药、体检等
- 教育: 学费、培训、书籍等
- 其他: 无法归类的支出
    `;

    return basePrompt;
  }

  /**
   * 调用豆包API
   */
  private async callDoubaoAPI(
    prompt: string,
    text: string,
    imageUrl?: string,
  ): Promise<BillingInfo> {
    const apiKey = this.configService.get<string>('DOUBAO_API_KEY');
    const endpoint = this.configService.get<string>('DOUBAO_API_ENDPOINT');
    const modelId = this.configService.get<string>('DOUBAO_MODEL_ID');

    if (!apiKey || !endpoint || !modelId) {
      this.logger.error('豆包 API 配置缺失');
      return this.createErrorResponse('AI 服务未正确配置');
    }

    const userContent: any[] = [];
    if (text) {
      userContent.push({ type: 'text', text });
    }
    if (imageUrl) {
      userContent.push({ type: 'image_url', image_url: { url: imageUrl } });
    }

    const requestBody = {
      model: modelId, // 使用环境变量中的模型端点ID
      messages: [
        {
          role: 'system',
          content: prompt,
        },
        {
          role: 'user',
          content: userContent,
        },
      ],
      temperature: 0.1, // 降低随机性，提高一致性
      max_tokens: 1000,
      stream: false,
    };

    try {
      const { data } = await firstValueFrom(
        this.httpService.post<DoubaoResponse>(endpoint, requestBody, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30秒超时
        }),
      );

      const responseContent = data.choices[0]?.message?.content;
      if (!responseContent) {
        return this.createErrorResponse('AI 服务返回空内容');
      }

      return this.parseAIResponse(responseContent);
    } catch (error) {
      this.logger.error('调用豆包 API 失败:', error);
      return this.createErrorResponse('调用大模型服务失败');
    }
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(content: string): BillingInfo {
    try {
      // 尝试提取JSON部分
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? jsonMatch[0] : content;

      const parsed = JSON.parse(jsonStr) as BillingInfo;

      // 验证必要字段
      if (typeof parsed.amount !== 'number' || parsed.amount < 0) {
        return this.createErrorResponse('金额格式不正确');
      }

      if (!parsed.category || !parsed.description) {
        return this.createErrorResponse('缺少必要的账单信息');
      }

      // 设置默认日期
      if (!parsed.date) {
        parsed.date = new Date().toISOString().split('T')[0];
      }

      // 设置默认置信度
      if (typeof parsed.confidence !== 'number') {
        parsed.confidence = 0.8;
      }

      return parsed;
    } catch (error) {
      this.logger.error('解析AI响应失败:', error);
      this.logger.debug('原始响应内容:', content);
      return this.createErrorResponse('无法解析AI服务返回的数据');
    }
  }

  /**
   * 创建错误响应
   */
  private createErrorResponse(errorMessage: string): BillingInfo {
    return {
      error: errorMessage,
      amount: 0,
      category: '其他',
      date: new Date().toISOString().split('T')[0],
      description: '处理失败',
      confidence: 0,
    };
  }

  /**
   * 验证账单信息
   */
  async validateBillingInfo(billingInfo: BillingInfo): Promise<boolean> {
    if (billingInfo.error) {
      return false;
    }

    if (billingInfo.amount <= 0) {
      return false;
    }

    const validCategories = [
      '餐饮', '交通', '购物', '娱乐', '住房', '医疗', '教育', '其他'
    ];

    if (!validCategories.includes(billingInfo.category)) {
      return false;
    }

    // 验证日期格式
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(billingInfo.date)) {
      return false;
    }

    return true;
  }
}
