generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String         @id @db.VarChar(255)
  name          String?        @db.VarChar(255)
  state         String?        @db.VarChar(50)
  usageCount    Int            @default(0)
  created_at    DateTime       @default(now()) @db.Timestamp(6)
  updated_at    DateTime       @updatedAt @db.Timestamp(6)
  subscriptions Subscription[]
  usageStats    UsageStat[]
  bindings      Binding[]
}

model Binding {
  id            String      @id @default(uuid()) @db.VarChar(36)
  user_id       String      @db.VarChar(255)
  type          BindingType
  access_token  String
  target_id     String      @db.VarChar(255)
  target_name   String?     @db.VarChar(255)
  is_active     Boolean     @default(false)
  created_at    DateTime    @default(now()) @db.Timestamp(6)
  updated_at    DateTime    @updatedAt @db.Timestamp(6)
  refresh_token String?
  user          User        @relation(fields: [user_id], references: [id])

  @@map("bindings")
}

model Subscription {
  id         String   @id @default(uuid()) @db.VarChar(36)
  user_id    String   @db.VarChar(255)
  plan       String   @db.VarChar(50)
  limit      Int      @default(1000)
  start_date DateTime @db.Timestamp(6)
  end_date   DateTime @db.Timestamp(6)
  status     String   @db.VarChar(50)
  created_at DateTime @default(now()) @db.Timestamp(6)
  user       User     @relation(fields: [user_id], references: [id])
}

model UsageStat {
  id         String   @id @default(uuid()) @db.VarChar(36)
  user_id    String   @db.VarChar(255)
  year_month Int
  count      Int      @default(0)
  updated_at DateTime @updatedAt @db.Timestamp(6)
  user       User     @relation(fields: [user_id], references: [id])

  @@unique([user_id, year_month], name: "user_id_year_month")
}

model WecomSyncCursor {
  id         String   @id @db.VarChar(255)
  cursor     String   @db.VarChar(255)
  updated_at DateTime @updatedAt @db.Timestamp(6)
}

enum BindingType {
  NOTION
  FEISHU
}
